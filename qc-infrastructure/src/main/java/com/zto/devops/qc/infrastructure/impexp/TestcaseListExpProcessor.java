package com.zto.devops.qc.infrastructure.impexp;

import com.zto.devops.framework.client.enums.impexp.ExpProcessorEnum;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.infrastructure.service.impexp.ExpProcessor;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ListTestcaseVO;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListTestcaseExpQuery;
import com.zto.devops.qc.domain.service.TestcaseQueryDomainService;
import com.zto.devops.qc.infrastructure.converter.TestcaseEntityConverter;
import com.zto.devops.qc.infrastructure.impexp.entity.TestcaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TestcaseListExpProcessor extends ExpProcessor<ListTestcaseExpQuery, TestcaseEntity> {

    @Autowired
    private TestcaseQueryDomainService testcaseQueryDomainService;
    @Autowired
    private TestcaseEntityConverter testcaseConverter;

    protected TestcaseListExpProcessor() {
        super(TestcaseEntity.class, ListTestcaseExpQuery.class, ExpProcessorEnum.EXP_TEST_CASE.name());
    }

    @Override
    protected String getFileName() {
        return "测试用例列表导出";
    }

    @Override
    public List<TestcaseEntity> pageData(ListTestcaseExpQuery query) {
        List<TestcaseEntity> out = new ArrayList<>();

        List<ListTestcaseVO> vos = testcaseQueryDomainService.handle(query);
//        if(CollectionUtil.isNotEmpty(vos)){
//            vos = vos.stream().filter(vo-> TestcaseAttributeEnum.TESTCASE.name().equals(vo.getAttribute())).collect(Collectors.toList());
//        }
        if (CollectionUtils.isEmpty(vos)) {
            return out;
        }
        log.info("EXP_TEST_CASE count : {}", vos.size());

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        List<ListTestcaseVO> sortList = vos.stream()
                .sorted((o1, o2) -> {
                    String[] p1 = o1.getParentIds().split(",");
                    String[] p2 = o2.getParentIds().split(",");

                    int minLen = Math.min(p1.length, p2.length);
                    for (int i = 0; i < minLen; i++) {
                        int cmp = Long.compare(Long.parseLong(p1[i]), Long.parseLong(p2[i]));
                        if (cmp != 0) return cmp;
                    }
                    //同分组按id排序
                    if (p1.length == p2.length) {
                        return Long.compare(o1.getId(), o2.getId());
                    }
                    return Integer.compare(p1.length, p2.length);
                }).collect(Collectors.toList());

        for (ListTestcaseVO vo : sortList) {
            TestcaseEntity testcaseEntity = testcaseConverter.convertor(vo);
            String stepDesc = "";
            String expectResult = "";
            if (CollectionUtil.isNotEmpty(vo.getTestSteps())) {
                stepDesc = StringUtils.join(vo.getTestSteps().stream().map(
                        testcaseStepVO -> {
                            return testcaseStepVO.getSort() + "." + testcaseStepVO.getStepDesc();
                        }).collect(Collectors.toList()), "\r\n");
                expectResult = StringUtils.join(vo.getTestSteps().stream().map(
                        testcaseStepVO -> {
                            return testcaseStepVO.getSort() + "." + testcaseStepVO.getExpectResult();
                        }).collect(Collectors.toList()), "\r\n");
            }
            testcaseEntity.setStepDesc(stepDesc);
            testcaseEntity.setExpectResult(expectResult);

            String tagName = "";
            if (CollectionUtil.isNotEmpty(vo.getTags())) {
                tagName = StringUtils.join(vo.getTags().stream().map(TagVO::getTagName).collect(Collectors.toList()), ";");
            }
            testcaseEntity.setTags(tagName);
            testcaseEntity.setGmtCreate(vo.getGmtCreate() == null ? "" : sdf.format(vo.getGmtCreate()));
            testcaseEntity.setGmtModified(vo.getGmtModified() == null ? "" : sdf.format(vo.getGmtModified()));
            out.add(testcaseEntity);
        }
        return out;
    }


}