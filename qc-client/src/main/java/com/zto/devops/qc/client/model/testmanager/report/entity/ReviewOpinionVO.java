package com.zto.devops.qc.client.model.testmanager.report.entity;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@GatewayModel(description = "评审意见")
@Data
public class ReviewOpinionVO implements Serializable {

    private static final long serialVersionUID = 4541924757216340535L;

    @ZModelProperty(description = "报告code", required = false, sample = "TR240416001001")
    private String reportCode;

    @ZModelProperty(description = "编号", required = false, sample = "123")
    private String serialId;

    @ZModelProperty(description = "评审意见code", required = false, sample = "TR240416001001")
    private String code;

    @ZModelProperty(description = "确认点以及修改项", required = false, sample = "确认点以及修改项")
    private String description;

    @ZModelProperty(description = "责任人ID", required = false, sample = "5664313")
    private Long ownerUserId;

    @ZModelProperty(description = "责任人名字", required = false, sample = "luban")
    private String ownerUserName;

    @ZModelProperty(description = "截至时间", required = false, sample = "1711987200000")
    private Date deadLineDate;

}
