package com.zto.devops.qc.domain.service;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.fastjson.JSON;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.LockSeal;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.framework.common.util.time.DateUtil;
import com.zto.devops.framework.domain.gateway.util.AggregateIdGenerateService;
import com.zto.devops.framework.domain.gateway.util.LockService;
import com.zto.devops.framework.domain.service.BaseDomainService;
import com.zto.devops.qc.client.enums.constants.LockStoreEnum;
import com.zto.devops.qc.client.enums.rpc.*;
import com.zto.devops.qc.client.enums.testmanager.channel.TestEventEnums;
import com.zto.devops.qc.client.enums.testmanager.coverage.*;
import com.zto.devops.qc.client.model.dto.CoverageRecordBasicEntityDO;
import com.zto.devops.qc.client.model.parameter.CoverageRecordEditParameter;
import com.zto.devops.qc.client.model.parameter.CoverageRecordGenerateParameter;
import com.zto.devops.qc.client.model.rpc.pipeline.*;
import com.zto.devops.qc.client.model.rpc.pipeline.event.*;
import com.zto.devops.qc.client.model.rpc.pipeline.query.FindOssFileUrlQuery;
import com.zto.devops.qc.client.model.rpc.pipeline.query.ListFlowVersionJacocoQuery;
import com.zto.devops.qc.client.model.rpc.pipeline.query.PageApplicationQuery;
import com.zto.devops.qc.client.model.rpc.product.SimpleQueryVO;
import com.zto.devops.qc.client.model.rpc.project.VersionInfoVO;
import com.zto.devops.qc.client.model.rpc.project.VersionVO;
import com.zto.devops.qc.client.model.testmanager.coverage.command.CoverageNotStandardReasonEditCommand;
import com.zto.devops.qc.client.model.testmanager.coverage.command.EditCoverageReasonCommand;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.*;
import com.zto.devops.qc.client.model.testmanager.coverage.event.CoverageNotStandardReasonEditEvent;
import com.zto.devops.qc.client.model.testmanager.coverage.event.CoverageReasonEditEvent;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoveragePublishQuery;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageRecordPageQuery;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageVersionRateQuery;
import com.zto.devops.qc.client.service.coverage.model.resp.VerifyGenerateConditionResp;
import com.zto.devops.qc.domain.converter.CoverageDomainConverter;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.gitlab.GitlabService;
import com.zto.devops.qc.domain.gateway.http.HttpService;
import com.zto.devops.qc.domain.gateway.jacoco.JacocoService;
import com.zto.devops.qc.domain.gateway.message.QcRobotMessageService;
import com.zto.devops.qc.domain.gateway.noticing.ReactiveEmitterService;
import com.zto.devops.qc.domain.gateway.oss.ZtoOssService;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.repository.CoverageRepository;
import com.zto.devops.qc.domain.gateway.repository.ITmTestPlanRepository;
import com.zto.devops.qc.domain.gateway.rpc.IPipelineRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import com.zto.devops.qc.domain.gateway.util.UncompressFileUtilService;
import com.zto.devops.qc.domain.util.DecompressionUtils;
import com.zto.devops.qc.domain.util.FileUtils;
import com.zto.devops.qc.domain.util.XmlUtils;
import com.zto.devops.qc.domain.util.ZipUtils;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.logging.FileHandler;
import java.util.logging.Logger;
import java.util.logging.SimpleFormatter;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022/9/22 10:55
 */
@Service
@Slf4j
public class CoverageDomainService extends BaseDomainService {

    private static ThreadPoolExecutor THREAD_POOL_EXECUTOR = new ThreadPoolExecutor(10, 50, 4, TimeUnit.MINUTES,
            new ArrayBlockingQueue<Runnable>(100),
            ThreadFactoryBuilder.create().setNamePrefix("EXECUTOR-REPORT-").build());

    private static ThreadPoolExecutor THREAD_POOL_EXECUTOR_EXEC = new ThreadPoolExecutor(20, 100, 60, TimeUnit.SECONDS,
            new ArrayBlockingQueue<Runnable>(100), ThreadFactoryBuilder.create().setNamePrefix("EXECUTOR-EXEC-").build());

    private static ThreadPoolExecutor THREAD_POOL_COVERAGE_ABORT = new ThreadPoolExecutor(1, 10, 4, TimeUnit.MINUTES,
            new ArrayBlockingQueue<Runnable>(100), ThreadFactoryBuilder.create().setNamePrefix("COVERAGE-ABORT-").build());

    private static ThreadPoolExecutor THREAD_POOL_PUBLISH = new ThreadPoolExecutor(10, 40, 4, TimeUnit.MINUTES,
            new ArrayBlockingQueue<Runnable>(100), ThreadFactoryBuilder.create().setNamePrefix("EXECUTOR-PUBLISH-").build());

    private static ThreadPoolExecutor THREAD_INTERFACE_COVERAGE_EXECUTOR = new ThreadPoolExecutor(20, 50, 10, TimeUnit.MINUTES,
            new ArrayBlockingQueue<Runnable>(100),
            ThreadFactoryBuilder.create().setNamePrefix("EXECUTOR-INTERFACE-COVERAGE-").build());

    private static final String AGENT_PREFIX = "AGENT_IP";
    private static final String DEPLOY_PREFIX = "DEPLOY";
    private static final String SCAN_PREFIX = "SCAN";

    @Autowired
    private QcConfigBasicService config;
    @Autowired
    private ReactiveEmitterService reactiveEmitter;
    @Autowired
    private CoverageDomainConverter coverageConverter;
    @Autowired
    private RedisService redisTemplate;
    @Autowired
    private CoverageQueryDomainService coverageQueryDomainService;
    @Autowired
    private CoverageRepository coverageRepository;
    @Autowired
    private ZtoOssService ossService;
    @Autowired
    private IProductRpcService productRpcService;
    @Autowired
    private IPipelineRpcService pipelineRpcService;
    @Autowired
    private IProjectRpcService projectRpcService;
    @Autowired
    private GitlabService gitlabService;
    @Autowired
    private QcRobotMessageService qcRobotMessageService;
    @Autowired
    private LockService lockService;
    @Autowired
    private AggregateIdGenerateService aggregateIdGenerateService;
    @Autowired
    private ITmTestPlanRepository tmTestPlanRepository;
    @Autowired
    private UncompressFileUtilService uncompressFileUtilService;
    @Autowired
    private JacocoService jacocoService;
    @Autowired
    private HttpService httpService;
    @Autowired
    private InterfaceCoverageDomainService interfaceCoverageDomainService;
    @Autowired
    private CoverageMergeService coverageMergeService;

    private static final Logger logger = Logger.getLogger(CoverageDomainService.class.getName());
    public static final String LOCAL_FILE_PATH = "/data/";
    private static final String SOURCE_PATH = "sources";
    private static final String CLASS_PATH = "classes/";
    private static final String REPORT_INDEX = "/coveragereport/index.html";

    private static final String NONE_OF_ANALYZED_CLASSES = "None of the analyzed classes contain code relevant for code coverage.";

    public void generateCoverageExec(CoveragePublishQuery query, DiffTypeEnum diffType) {
        if (CollectionUtil.isEmpty(query.getVersionCodes())) {
            throw new ServiceException("版本编号versionCodes不能为空");
        }
        List<CoveragePublishVO> coveragePublishVOList = coverageRepository.getLatestPublishRecord(query);
        if (CollectionUtil.isEmpty(coveragePublishVOList)) {
            throw new ServiceException("没有部署记录，无法生存exec文件");
        }
        for (CoveragePublishVO entity : coveragePublishVOList) {
            if (StringUtil.isEmpty(entity.getServiceName())) {
                log.warn("ip或实例名serviceName为空,appId = " + entity.getAppId() + ", " + "versionName" + " = " + entity.getVersionName() + ", versionCode = " + entity.getVersionCode());
                continue;
            }
            THREAD_POOL_EXECUTOR_EXEC.execute(() -> {
                List<String> ipList = getPodIpList(entity);
                if (CollectionUtils.isEmpty(ipList)) {
                    log.warn("获取podIp为空,coveragePublishId->{},serviceName->{} ", entity.getId(), entity.getServiceName());
                } else {
                    generateExec(ipList, entity, query, logger, diffType);
                }
            });
        }
    }

    /**
     * 生成exec文件，记录日志
     *
     * @param entity
     * @param query
     */
    public void generateCoverageExec(CoveragePublishVO entity, CoveragePublishQuery query, Logger logger, DiffTypeEnum diffType) {
        logger.info("开始生成exec文件");
        if (StringUtil.isEmpty(entity.getVersionCode())) {
            throw new ServiceException("版本编号versionCode不能为空");
        }
        CoveragePublishVO latest = coverageRepository.getLatestPublishRecordByEntity(entity);
        if (null == latest) {
            throw new ServiceException(RecordErrorMsgEnum.APP_PUBLISH_RECORD_EMPTY.getValue());
        }
        List<CoveragePublishVO> latestCommitList = coverageRepository.getByCommitId(latest.getVersionCode(), latest.getCommitId(), latest.getAppId());
        if (CollectionUtil.isEmpty(latestCommitList)) {
            throw new ServiceException(RecordErrorMsgEnum.APP_PUBLISH_RECORD_EMPTY.getValue());
        }
        Set<String> serviceNames = latestCommitList.stream().map(CoveragePublishVO::getServiceName).filter(StringUtil::isNotBlank).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(serviceNames)) {
            throw new ServiceException(RecordErrorMsgEnum.SERVICE_NAME_OR_IP_IS_NULL.getValue());
        }

        List<String> ipList = getPodIpListByServiceNames(serviceNames);
        if (CollectionUtils.isEmpty(ipList)) {
            throw new ServiceException(RecordErrorMsgEnum.POD_IP_IS_NULL.getValue());
        }
        boolean flag = generateExec(ipList, latest, query, logger, diffType);
        if (!flag) {
            throw new ServiceException("生成并上传exec文件失败。请确认Jacoco开关开启后重新部署。如果还有问题请联系值班人员");
        }
    }

    private List<String> getPodIpListByServiceNames(Set<String> serviceNames) {
        if (CollectionUtil.isEmpty(serviceNames)) {
            return new ArrayList<>();
        }
        List<String> instanceNameList = serviceNames.stream()
                .flatMap(item -> Arrays.stream(StringUtils.split(item, ",")))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(instanceNameList)) {
            return new ArrayList<>();
        }
        Set<String> resultList = new HashSet<>(instanceNameList.size());
        resultList.addAll(instanceNameList.stream().filter(this::isIpAddress).collect(Collectors.toSet()));
        List<String> toQueryList = instanceNameList.stream().filter(name -> !isIpAddress(name))
                .map(name -> name.contains(".") ? name.split("\\.")[0] : name).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(toQueryList)) {
            resultList.addAll(getPodIpListByInstanceNameList(toQueryList));
        }
        return new ArrayList<>(resultList);
    }

    public void saveCoverageErrorMsg(VerifyGenerateVO generateVO, CoverageRecordGenerateParameter parameter) {
        log.info("saveCoverageErrorMsg_generateVO:{},parameter:{}", JSON.toJSON(generateVO), JSON.toJSON(parameter));
        if (null == generateVO || null == parameter) {
            return;
        }
        generateVO.getReasonList().forEach(reason -> {
            reason.getAppIdList().forEach(appId -> {
                doSaveCoverageRecord(parameter, reason, appId, RecordStatusEnum.getVerifyResult(generateVO.getStatus()));
            });
        });
    }

    private void doSaveCoverageRecord(CoverageRecordGenerateParameter parameter, VerifyGenerateReasonVO reason, String appId, RecordStatusEnum status) {
        redisTemplate.delete(parameter.getVersionCode() + appId + parameter.getRecordType());
        CoverageRecordBasicVO entity = coverageRepository.getInitialCoverageRecordByAppId(parameter, appId);
        if (null == entity) {
            log.info("doSaveCoverageRecord_entity_is_null_appId:{}_param:{}", appId, JSON.toJSON(parameter));
            return;
        }
        entity.setRecordErrorMsg(reason.getReason());
        //白名单||没有实现类，则回填原因
        if (reason.getReason().equals(RecordErrorMsgEnum.WHITE_LIST_APP.getValue())) {
            entity.addBasicVORemark(RecordRemarkEnum.WHITE_LIST_APP.getValue());
        }
        saveCoverageRecord(entity, null, null, parameter, status, null);
    }

    /**
     * 校验结束删除key
     */
    @Async
    public void deleteVerifyKey(String versionCode, RecordTypeEnum recordType, List<String> appIdList) {
        if (StringUtil.isBlank(versionCode) || null == recordType || CollectionUtil.isEmpty(appIdList)) {
            return;
        }
        for (String appId : appIdList) {
            redisTemplate.delete("verify_" + versionCode + appId);
        }
    }

    /**
     * 前置校验覆盖率生成条件
     *
     * @param parameter
     */
    public VerifyGenerateConditionResp verifyGenerateCondition(CoverageRecordGenerateParameter parameter, VersionInfoVO versionInfoVO) {
        log.info("verifyGenerateCondition_parameter: {}", JsonUtil.toJSON(parameter));
        setFlowLaneType(parameter, versionInfoVO);
        List<CoverageRecordBasicVO> initialList = coverageRepository.getInitialCoverageRecords(parameter);
        if (CollectionUtil.isEmpty(initialList)) {
            throw new ServiceException("未查询到待生成应用记录");
        }
        List<VerifyGenerateReasonFlatVO> dataList = new ArrayList<>();
        boolean passFlag = batchPreVerify(initialList, dataList, parameter, versionInfoVO);
        if (passFlag) {
            for (CoverageRecordBasicVO entity : initialList) {
                entity.setFlowLaneType(FlowLaneTypeEnum.valueOf(parameter.getFlowLaneType()));
                preVerify(entity, parameter, dataList);
            }
        }
        return VerifyGenerateConditionResp.buildSelf(dataList, initialList);
    }

    private boolean batchPreVerify(List<CoverageRecordBasicVO> initialList,
                                   List<VerifyGenerateReasonFlatVO> dataList,
                                   CoverageRecordGenerateParameter parameter,
                                   VersionInfoVO versionInfoVO) {
        Set<String> appIdSet = initialList.stream().map(CoverageRecordBasicVO::getAppId).collect(Collectors.toSet());
        CoverageRecordBasicVO sample = initialList.get(0);
        //校验部门信息
        boolean passFlag = verifyDept(sample, parameter, dataList, appIdSet);
        if (passFlag) {
            //校验版本信息
            passFlag = verifyVersionInfo(parameter, dataList, versionInfoVO, appIdSet);
        }
        return passFlag;
    }

    private void preVerify(CoverageRecordBasicVO entity,
                           CoverageRecordGenerateParameter parameter,
                           List<VerifyGenerateReasonFlatVO> dataList) {
        //校验应用信息
        boolean passFlag = verifyApplication(entity, parameter, dataList);
        if (!passFlag) {
            return;
        }
        //校验待生成数据
        passFlag = verifyGenerateEntity(entity, dataList);
        if (!passFlag) {
            return;
        }
        //校验执行文件
        passFlag = verifyExec(entity, dataList);
        if (!passFlag) {
            return;
        }
        //通过
        dataList.add(VerifyGenerateReasonFlatVO.buildSelf(RecordStatusEnum.SUCCEED,
                entity.getAppId(), null));
    }

    private boolean verifyExec(CoverageRecordBasicVO entity, List<VerifyGenerateReasonFlatVO> dataList) {
        CoveragePublishVO toQuery = new CoveragePublishVO();
        toQuery.setVersionCode(entity.getVersionCode());
        toQuery.setBranchName(entity.getBranchName());
        toQuery.setAppId(entity.getAppId());
        CoveragePublishVO coveragePublishVO = coverageRepository.getLatestPublishRecordByEntity(toQuery);
        if (null == coveragePublishVO) {
            dataList.add(VerifyGenerateReasonFlatVO.buildSelf(RecordStatusEnum.FAIL,
                    entity.getAppId(), RecordErrorMsgEnum.APP_PUBLISH_RECORD_EMPTY));
            return Boolean.FALSE;
        }
        if (StringUtil.isEmpty(coveragePublishVO.getServiceName())) {
            dataList.add(VerifyGenerateReasonFlatVO.buildSelf(RecordStatusEnum.FAIL,
                    entity.getAppId(), RecordErrorMsgEnum.SERVICE_NAME_OR_IP_IS_NULL));
            return Boolean.FALSE;
        }
        List<String> ipList = getPodIpList(coveragePublishVO);
        if (CollectionUtils.isEmpty(ipList)) {
            dataList.add(VerifyGenerateReasonFlatVO.buildSelf(RecordStatusEnum.FAIL,
                    entity.getAppId(), RecordErrorMsgEnum.POD_IP_IS_NULL));
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private boolean verifyDept(CoverageRecordBasicVO entity,
                               CoverageRecordGenerateParameter parameter,
                               List<VerifyGenerateReasonFlatVO> dataList,
                               Set<String> appIdSet) {
        if (null != entity.getDeptId() && entity.getDeptId() != 0L) {
            return Boolean.TRUE;
        }
        try {
            SimpleQueryVO productVO = getProductVO(parameter.getProductCode());
            if (null == productVO) {
                dataList.addAll(VerifyGenerateReasonFlatVO.buildList(RecordStatusEnum.FAIL,
                        appIdSet, RecordErrorMsgEnum.GET_PRODUCT_INFO_EMPTY));
                return Boolean.FALSE;
            }
        } catch (Exception e) {
            dataList.addAll(VerifyGenerateReasonFlatVO.buildList(RecordStatusEnum.FAIL,
                    appIdSet, RecordErrorMsgEnum.GET_PRODUCT_INFO_ERROR));
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private boolean verifyApplication(CoverageRecordBasicVO entity,
                                      CoverageRecordGenerateParameter parameter,
                                      List<VerifyGenerateReasonFlatVO> dataList) {
        try {
            ApplicationVO applicationDetail = getApplicationDetail(entity.getAppId(), parameter.getProductCode());
            if (null == applicationDetail) {
                dataList.add(VerifyGenerateReasonFlatVO.buildSelf(RecordStatusEnum.FAIL,
                        entity.getAppId(), RecordErrorMsgEnum.GET_APP_INFO_EMPTY));
                return Boolean.FALSE;
            }
            if (applicationDetail.getWhiteList()) {
                dataList.add(VerifyGenerateReasonFlatVO.buildSelf(RecordStatusEnum.NEEDLESS,
                        entity.getAppId(), RecordErrorMsgEnum.WHITE_LIST_APP));
                return Boolean.FALSE;
            }
        } catch (Exception e) {
            dataList.add(VerifyGenerateReasonFlatVO.buildSelf(RecordStatusEnum.FAIL,
                    entity.getAppId(), RecordErrorMsgEnum.GET_APP_INFO_ERROR));
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private boolean verifyVersionInfo(CoverageRecordGenerateParameter parameter,
                                      List<VerifyGenerateReasonFlatVO> dataList,
                                      VersionInfoVO versionInfoVO,
                                      Set<String> appIdSet) {
        try {
            String testStrategy;
            if (null == versionInfoVO) {
                testStrategy = getVersionInfo(parameter.getVersionCode()).getTestStrategy();
            } else {
                testStrategy = VersionTypeEnum.URGENT_TYPE.name().equals(versionInfoVO.getType())
                        ? VersionTypeEnum.URGENT_TYPE.name()
                        : versionInfoVO.getTestStrategy();
            }
            if (StringUtil.isEmpty(testStrategy)) {
                dataList.addAll(VerifyGenerateReasonFlatVO.buildList(RecordStatusEnum.FAIL,
                        appIdSet, RecordErrorMsgEnum.GET_TEST_STRATEGY_EMPTY));
                return Boolean.FALSE;
            }
        } catch (Exception e) {
            dataList.addAll(VerifyGenerateReasonFlatVO.buildList(RecordStatusEnum.FAIL,
                    appIdSet, RecordErrorMsgEnum.GET_TEST_STRATEGY_ERROR));
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    private boolean verifyGenerateEntity(CoverageRecordBasicVO entity,
                                         List<VerifyGenerateReasonFlatVO> dataList) {
        if (null == entity.getFlowLaneType()) {
            dataList.add(VerifyGenerateReasonFlatVO.buildSelf(RecordStatusEnum.FAIL,
                    entity.getAppId(), RecordErrorMsgEnum.FLOW_LANE_IS_EMPTY));
            return Boolean.FALSE;
        }
        CoverageRecordGenerateVO generateEntity = coverageRepository.selectCoverageRecords(entity);
        if (null == generateEntity) {
            dataList.add(VerifyGenerateReasonFlatVO.buildSelf(RecordStatusEnum.FAIL,
                    entity.getAppId(), RecordErrorMsgEnum.APP_PUBLISH_RECORD_EMPTY));
            return Boolean.FALSE;
        }
        generateEntity.setFlowLaneType(entity.getFlowLaneType().name());
        generateEntity.setDiffType(entity.getDiffType());
        generateEntity.setMergeDump(CoverageMergeEnum.MERGE.getValue());
        if (StringUtil.isEmpty(generateEntity.getGitUrl()) && generateEntity.getRecordType().equals(RecordTypeEnum.BRANCH)) {
            dataList.add(VerifyGenerateReasonFlatVO.buildSelf(RecordStatusEnum.FAIL,
                    entity.getAppId(), RecordErrorMsgEnum.NOT_PUBLISHED_TEST_ENV));
            return Boolean.FALSE;
        }
        String filterUnComparedProducts = config.getCoverageConfig().getFilterUnComparedProducts();
        if ((StringUtil.isEmpty(filterUnComparedProducts)
                || !Arrays.stream(filterUnComparedProducts.split(",")).anyMatch(obj -> obj.equals(entity.getProductCode())))) {
            try {
                boolean containFlag = gitlabService.targetContainSource(generateEntity.getGitProjectId().intValue(), generateEntity.getBasicBranchName(), generateEntity.getBranchName());
                if (!containFlag) {
                    dataList.add(VerifyGenerateReasonFlatVO.buildSelf(RecordStatusEnum.FAIL,
                            entity.getAppId(), RecordErrorMsgEnum.RELEASE_BEHIND_MASTER));
                    return Boolean.FALSE;
                }
            } catch (Exception e) {
                log.error("检测目标分支是否包含源分支异常", e);
                dataList.add(VerifyGenerateReasonFlatVO.buildSelf(RecordStatusEnum.FAIL,
                        entity.getAppId(), RecordErrorMsgEnum.RELEASE_BEHIND_MASTER_CHECK_ERROR));
                return Boolean.FALSE;
            }
            try {
                boolean isDeployed = isDeployedLatestCommitId(generateEntity);
                if (!isDeployed) {
                    dataList.add(VerifyGenerateReasonFlatVO.buildSelf(RecordStatusEnum.FAIL,
                            entity.getAppId(), RecordErrorMsgEnum.COMMIT_ID_NOT_DEPLOYED));
                    return Boolean.FALSE;
                }
            } catch (Exception e) {
                log.error("检测当前分支是否部署过最新commitId异常", e);
                dataList.add(VerifyGenerateReasonFlatVO.buildSelf(RecordStatusEnum.FAIL,
                        entity.getAppId(), RecordErrorMsgEnum.COMMIT_ID_NOT_DEPLOYED_CHECK_ERROR));
                return Boolean.FALSE;
            }
        }
        String firstBranchCommitId = getFirstBranchCommitId(generateEntity);
        if (generateEntity.getCommitId().equals(firstBranchCommitId)
                || generateEntity.getCommitId().equals(generateEntity.getBasicCommitId())) {
            dataList.add(VerifyGenerateReasonFlatVO.buildSelf(RecordStatusEnum.NEEDLESS,
                    entity.getAppId(), RecordErrorMsgEnum.COMMIT_ID_IS_IDENTICAL));
            return Boolean.FALSE;
        }
        if ((RecordTypeEnum.BRANCH.equals(entity.getRecordType())) && StringUtils.isEmpty(generateEntity.getBasicCommitId())) {
            dataList.add(VerifyGenerateReasonFlatVO.buildSelf(RecordStatusEnum.FAIL,
                    entity.getAppId(), RecordErrorMsgEnum.BASIC_COMMIT_ID_IS_EMPTY));
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 前置校验-设置发布泳道
     *
     * @param parameter
     */
    private void setFlowLaneType(CoverageRecordGenerateParameter parameter, VersionInfoVO versionInfoVO) {
        String flowLaneType = this.getFlowLaneType(parameter.getVersionCode(), versionInfoVO);
        if (StringUtils.isEmpty(flowLaneType)) {
            throw new ServiceException("当前版本发布泳道为空。versionCode = " + parameter.getVersionCode());
        }
        parameter.setFlowLaneType(flowLaneType);
    }

    /**
     * 测试通过自动触发覆盖率完成，触发产品群通知.
     */
    private void sendCoverageResultMessage(CoverageRecordGenerateParameter parameter) {
        log.info("sendCoverageResultMessage_param:{}", JSON.toJSON(parameter));
        String key = "AUTO_GENERATE_" + parameter.getProductCode() + "_" + parameter.getVersionCode();
        String value = redisTemplate.getKey(key);
        log.info("sendCoverageResultMessage_key:{}_value:{}", key, value);
        if (StringUtil.isNotBlank(value)) {
            List<String> appIdList = Arrays.asList(value.split(","));
            List<CoverageRecordBasicEntityDO> entityDOList =
                    coverageRepository.selectAutoGenerateResultByAppIdList(parameter.getVersionCode(), appIdList);
            if (CollectionUtil.isEmpty(entityDOList)) {
                log.debug("sendCoverageResultMessage_entityList_is_null_versionCode:{},value:{}", parameter.getVersionCode(), value);
                return;
            }
            if (entityDOList.size() != appIdList.size()) {
                //有数据正在生成中，个数对不上
                log.info("sendCoverageResultMessage_entityList_wrong_size:{}", JSON.toJSON(entityDOList));
                return;
            }
            boolean sendFlag = Boolean.TRUE;
            for (CoverageRecordBasicEntityDO entity : entityDOList) {
                if (entity.getStatus().equals(RecordStatusEnum.RUNNING)
                        || (entity.getStatus().equals(RecordStatusEnum.INITIAL) && StringUtil.isBlank(entity.getRecordErrorMsg()))) {
                    //数据正在生成中 || 尚未生成过
                    sendFlag = Boolean.FALSE;
                    break;
                }
            }
            log.info("sendCoverageResultMessage_sendFlag:{}", sendFlag);
            if (sendFlag) {
                sendMessage(parameter);
            }
        }
    }

    /**
     * 消息发送加锁
     *
     * @param parameter
     */
    public void sendMessage(CoverageRecordGenerateParameter parameter) {
        log.info("sendCoverageResultMessage_lock_in_taskId: {}", parameter.getTaskId());
        try {
            LockSeal lockSeal = lockService.acquireLock(LockStoreEnum.COVERAGE_MESSAGE.getValue(),
                    Collections.singletonList(parameter.getTaskId()), 5000L);
            log.info("sendCoverageResultMessage_lock: {}", (null != lockSeal ? lockSeal.getKeys() : "null"));
            if (null != lockSeal) {
                qcRobotMessageService.sendCoverageRateFailMessageEvent(parameter);
            }
        } catch (Exception e) {
            log.error("sendCoverageResultMessage_lock_error_taskId: {}", parameter.getTaskId());
        }
    }

    private void setAutoGenerateKey(List<CoverageRecordBasicVO> initialCoverageRecords, String productCode, String versionCode) {
        String key = "AUTO_GENERATE_" + productCode + "_" + versionCode;
        String value = redisTemplate.getKey(key);
        if (StringUtil.isNotBlank(value)) {
            List<String> appIdList = initialCoverageRecords.stream().map(CoverageRecordBasicVO::getAppId).collect(Collectors.toList());
            redisTemplate.setKey(key, String.join(",", appIdList));
            log.info("setAutoGenerateKey_setValue:{}", redisTemplate.getKey(key));
        }
    }

    public void generateCoverageReport(CoverageRecordGenerateParameter parameter) {
        log.info(">>>>>>CoverageRecordGenerateParameter : {}", JsonUtil.toJSON(parameter));
        String flowLaneType = this.getFlowLaneType(parameter.getVersionCode(), null);
        if (StringUtil.isEmpty(flowLaneType)) {
            throw new ServiceException("当前版本发布泳道为空。versionCode = " + parameter.getVersionCode());
        }
        List<CoverageRecordBasicVO> initialCoverageRecords = coverageRepository.getInitialCoverageRecords(parameter);
        if (CollectionUtil.isEmpty(initialCoverageRecords)) {
            log.error("生成覆盖率报告失败，未查询到待生成应用记录。versionCode : {}, recordType : {}, appIdList : {}", parameter.getVersionCode(), parameter.getRecordType(), parameter.getAppIdList());
            for (String appId : parameter.getAppIdList()) {
                redisTemplate.delete(parameter.getVersionCode() + appId + parameter.getRecordType());
            }
            throw new ServiceException("未查询到待生成应用记录");
        }
        setAutoGenerateKey(initialCoverageRecords, parameter.getProductCode(), parameter.getVersionCode());
        for (CoverageRecordBasicVO entity : initialCoverageRecords) {
            entity.setFlowLaneType(FlowLaneTypeEnum.valueOf(flowLaneType));
            String localLogFile = LOCAL_FILE_PATH + entity.getVersionCode() + "-" + entity.getBranchName() + "-" + entity.getAppId() + "/logs/" + entity.getId() + ".log";
            final CoverageDomainService coverageManage = new CoverageDomainService();
            FileHandler fh = coverageManage.createExecuteLog(localLogFile,
                    LOCAL_FILE_PATH + entity.getVersionCode() + "-" + entity.getBranchName() + "-" + entity.getAppId() + "/logs/");
            if (null == fh) {
                log.error("创建执行日志异常，versionCode : " + entity.getVersionCode() + ", appId : " + entity.getAppId());
                entity.setRecordErrorMsg(RecordErrorMsgEnum.CREATE_EXECUTE_LOG_ERROR.getValue());
                entity.setStatus(RecordStatusEnum.FAIL);
                entity.setGmtModified(new Date());
                coverageRepository.updateByPrimaryKeySelective(entity);
                // 生成失败删除缓存
                redisTemplate.delete(entity.getVersionCode() + entity.getAppId() + entity.getRecordType());
                CoverageRecordPageQuery query = new CoverageRecordPageQuery();
                query.setVersionCode(parameter.getVersionCode());
                query.setAppId(entity.getAppId());
                CoverageRecordVO coverageRecordVO = coverageRepository.getCoverageRecordList(query).get(0);
                CoverageStatusVO coverageStatusVO = coverageConverter.converter(coverageRecordVO);
                queryVersionCoverageRate(coverageStatusVO);
                reactiveEmitter.emit(TestEventEnums.TEST_CODE_COVER_GENERATE.toReactiveId(parameter.getVersionCode()), coverageStatusVO);
                sendCoverageResultMessage(parameter);
                continue;
            }
            THREAD_POOL_EXECUTOR.execute(() -> {
                startGenerateReport(entity, parameter, logger, localLogFile, fh);
            });
        }
    }

    public void saveCoveragePublish(List<JacocoApplicationVO> apps, User user) {
        this.saveCoveragePublish(apps, user, true);
    }

    public void saveCoveragePublish(List<JacocoApplicationVO> apps, User user, boolean ifInsertCoverageRepository) {
        if (ifInsertCoverageRepository) {
            log.info("保存应用部署信息:{}", JsonUtil.toJSON(apps));
        } else {
            log.info("实例进程PID发生变化， 重启、回滚、部署事件信息:{}", JsonUtil.toJSON(apps));
        }
        for (JacocoApplicationVO app : apps) {
            if (CollectionUtil.isEmpty(app.getVersions())) {
                throw new ServiceException("版本信息versions不能为空");
            }
            if (CollectionUtils.isEmpty(app.getInstances())) {
                throw new ServiceException("实例信息instances不能为空");
            }
            String serviceName = getServiceName(app.getInstances());
            for (Version version : app.getVersions()) {
                CoveragePublishVO entity = new CoveragePublishVO();
                BeanUtils.copyProperties(app, entity);
                entity.setVersionCode(version.getCode());
                entity.setServiceName(serviceName);
                entity.setVersionName(version.getName());
                entity.setEnvName(app.getNamespaceName());
                entity.setAppType(AppTypeEnum.JAVA);
                entity.setPort(app.getPort() != null ? app.getPort() : 6300);
                entity.setDeploymentIdentityEnum(DeploymentIdentityEnum.DOCKER);
                entity.setCreator(user.getUserName());
                entity.setCreatorId(user.getUserId());
                entity.setModifier(user.getUserName());
                entity.setModifierId(user.getUserId());
                entity.setFlowLaneType(app.getFlowLaneType() != null ? app.getFlowLaneType().name() : "");
                if (ifInsertCoverageRepository) {
                    coverageRepository.insertSelective(entity);
                }
                THREAD_POOL_PUBLISH.execute(() -> {
                    String key = AGENT_PREFIX + "_" + app.getAppId() + "_" + entity.getVersionCode();
                    if (NamespaceTypeEnum.BASE == app.getType()) {
                        key = AGENT_PREFIX + "_" + app.getAppId() + "_" + "base"; // 与应用部署的titans.dubbo.tag保持一致
                    }
                    if (NamespaceTypeEnum.BASE_TEST == app.getType()) {
                        key = AGENT_PREFIX + "_" + app.getAppId() + "_" + "test"; // 与应用部署的titans.dubbo.tag保持一致
                    }
                    setAgentIpCache(entity, key);
                });
                THREAD_INTERFACE_COVERAGE_EXECUTOR.execute(() -> {
                    if (ifInsertCoverageRepository) {
                        interfaceCoverageDomainService.interfaceCoverageHandle(entity, user);
                    }
                });
            }
        }
    }

    private void setAgentIpCache(CoveragePublishVO entity, String key) {
        List<String> ips = getPodIpList(entity);
        if (CollectionUtil.isEmpty(ips)) {
            return;
        }
        if (redisTemplate.hasKey(key)) {
            log.info("old ips : {}", redisTemplate.opsForZSetRang(key, 0, -1));
            Set<String> oldIPS = redisTemplate.opsForZSetRang(key, 0, -1);
            if (CollectionUtil.isNotEmpty(oldIPS)) {
                for (String ip : oldIPS) {
                    String oldAgentIpKey = AGENT_PREFIX + "_" + entity.getAppId() + "_" + ip;
                    redisTemplate.delete((oldAgentIpKey));
                }
            }
            redisTemplate.delete(key);
        }
        for (String ip : ips) {
            // 保存版本与ip关系
            redisTemplate.opsForZSetAddLast(key, ip);
            // 保存ip与版本关系
            String agentIpKey = AGENT_PREFIX + "_" + entity.getAppId() + "_" + ip;
            redisTemplate.setKey(agentIpKey, entity.getVersionCode(), 20, TimeUnit.DAYS);
        }
        log.info("new ips : {}", redisTemplate.opsForZSetRang(key, 0, -1));
    }

    private CoverageRecordGenerateVO buildFromCoveragePublishVO(CoveragePublishVO publishVO, boolean isModules) {
        CoverageRecordGenerateVO generateVO = new CoverageRecordGenerateVO();
        generateVO.setBranchName(publishVO.getBranchName());
        generateVO.setCommitId(publishVO.getCommitId());
        generateVO.setVersionCode(publishVO.getVersionCode());
        generateVO.setGitUrl(publishVO.getGitUrl());
        generateVO.setAppId(publishVO.getAppId());
        generateVO.setPackageName(publishVO.getPackageName());
        generateVO.setOutputFileName(publishVO.getOutputFileName());
        buildGenerateVOPName(generateVO, publishVO.getBranchName());
        buildGenerateVODownloadUrl(generateVO, isModules);
        return generateVO;
    }

    /**
     * 下载git代码
     */
    private void downloadGitFile(CoverageRecordGenerateVO generateVO, String sourcePath) {
        try {
            log.info("开始从Git下载源码");
            gitlabService.clone(generateVO, sourcePath);
        } catch (ServiceException e) {
            log.error("Git下载源码异常.", e);
            throw new ServiceException(e.getMsg());
        } catch (Exception e) {
            log.error("Git下载源码异常.", e);
            throw new ServiceException("Git下载源码异常,请稍后再试");
        }
        log.info("下载Git源码成功！");
    }

    /**
     * 下载合并后的文件
     *
     * @param generateVO
     * @param localPath
     */
    private void downloadOutputFile(CoverageRecordGenerateVO generateVO, String localPath) {
        String zipFileName = "";
        try {
            zipFileName = downloadOutputFileZip(localPath, generateVO.getDownloadUrl());
            ZipUtils.unZip(localPath + zipFileName, localPath);
            jacocoService.setLocalClassPath(generateVO, localPath + CLASS_PATH);
            log.info("versionCode : {}, branchName : {}, appId :{}, localClassesPath : {}", generateVO.getVersionCode(), generateVO.getBranchName(), generateVO.getAppId(), generateVO.getLocalClassesPath());
        } catch (ServiceException e) {
            log.error("下载合并后的文件异常", e);
            throw new ServiceException(e.getMsg());
        } catch (Exception e) {
            log.error("下载合并后的文件异常", e);
            throw new ServiceException(e.getMessage());
        } finally {
            if (StringUtil.isNotBlank(zipFileName)) {
                FileUtils.deleteDir(new File(localPath + zipFileName));
            }
        }
    }

    /**
     * 删除class文件和git代码
     */
    public void deleteGitFileAndClassSource(String versionCode, String branchName, String appId, String commitId) {
        log.info("删除源代码和class包，versionCode:{}，branchName:{}, appId:{}, commitId:{}", versionCode, branchName, appId, commitId);
        try {
            String rmPath = LOCAL_FILE_PATH + versionCode + "-" + branchName + "-" + appId + "/" + branchName + "/";
            File dir = new File(rmPath);
            FileUtils.deleteDir(dir);
            log.info("删除成功！path : {}", rmPath);
        } catch (Exception e) {
            log.error("删除源代码和class包异常", e);
            throw new ServiceException("删除源代码和class包异常！");
        }
    }

    public void initCoverageRecords(List<BaseApplicationDO> newRelatedApps,
                                    List<BaseApplicationDO> cancelApps,
                                    String versionCode,
                                    String branchName,
                                    String releaseBranchName) {
        CoverageRecordBasicVO basicEntity = new CoverageRecordBasicVO(null, versionCode, "", branchName, null, null, Boolean.TRUE, AppTypeEnum.JAVA);
        List<String> branchNames = new ArrayList<>();
        if (StringUtil.isNotEmpty(branchName)) {
            branchNames.add(branchName);
        }
        if (StringUtil.isNotEmpty(releaseBranchName)) {
            branchNames.add(releaseBranchName);
        } else {
            branchNames.add(BranchEnum.PROD.name());
        }
        List<CoverageRecordBasicVO> existList = coverageRepository.getExistCoverageRecords(versionCode, branchNames, AppTypeEnum.JAVA.name());
        List<CoverageRecordBasicVO> newList = buildNewList(newRelatedApps, basicEntity, releaseBranchName);
        if (CollectionUtil.isEmpty(existList) && CollectionUtil.isNotEmpty(newList)) {
            coverageRepository.batchInsert(newList);
        } else {
            // 需要删除的数据
            doDeleteCancelApps(cancelApps, versionCode, branchNames);
            // 需要新增的数据
            List<CoverageRecordBasicVO> insertList = diffList(newList, existList);
            log.info(versionCode + "_doInsertApps_branchNames->{}_cancelList->{}", JsonUtil.toJSON(branchNames), JsonUtil.toJSON(insertList));
            if (CollectionUtil.isNotEmpty(insertList)) {
                for (CoverageRecordBasicVO entity : insertList) {
                    entity.setStatus(RecordStatusEnum.INITIAL);
                    coverageRepository.insertCoverageRecordBasic(entity);
                }
            }
        }
    }

    /**
     * 删除取消关联应用数据
     *
     * @param cancelApps  取消关联应用
     * @param versionCode 版本code
     * @param branchNames 分支名称
     */
    private void doDeleteCancelApps(List<BaseApplicationDO> cancelApps, String versionCode, List<String> branchNames) {
        if (CollectionUtil.isEmpty(cancelApps)) {
            return;
        }
        List<String> cancelAppIdList = cancelApps.stream().map(BaseApplicationDO::getAppId).collect(Collectors.toList());
        List<CoverageRecordBasicVO> cancelList =
                coverageRepository.getExistCoverageRecordsByAppIdList(versionCode, branchNames, AppTypeEnum.JAVA.name(), cancelAppIdList);
        log.info(versionCode + "_doDeleteCancelApps_branchNames->{}_cancelList->{}", JsonUtil.toJSON(branchNames), JsonUtil.toJSON(cancelAppIdList));
        if (CollectionUtil.isEmpty(cancelList)) {
            return;
        }
        for (CoverageRecordBasicVO entity : cancelList) {
            entity.setStatus(RecordStatusEnum.NEEDLESS);
            entity.setRemark(RecordRemarkEnum.CANCEL_APPLICATION_RELATION.getValue());
            entity.setGmtModified(new Date());
            entity.setEnable(Boolean.FALSE);
            coverageRepository.updateByPrimaryKeySelective(entity);
        }
    }

    /**
     * 删除取消关联应用数据
     *
     * @param cancelList 取消关联应用
     */
    private void doDeleteCancelApps(List<CoverageRecordBasicVO> cancelList) {
        if (CollectionUtil.isEmpty(cancelList)) {
            return;
        }
        for (CoverageRecordBasicVO entity : cancelList) {
            entity.setStatus(RecordStatusEnum.NEEDLESS);
            entity.setRemark(RecordRemarkEnum.CANCEL_APPLICATION_RELATION.getValue());
            entity.setGmtModified(new Date());
            entity.setEnable(Boolean.FALSE);
            coverageRepository.updateByPrimaryKeySelective(entity);
        }
    }

    public void createBranch(CreateBranchQcEvent event) {
        List<CoverageBranchBasicVO> entityList = new ArrayList<>();
        for (CreateBranchQcEvent.Version version : event.getVersions()) {
            if (CollectionUtil.isEmpty(version.getApps())) {
                log.warn("创建分支基础信息集合为空。versionCode : {}", version.getCode());
                continue;
            }
            for (BaseApplicationDO app : version.getApps()) {
                CoverageBranchBasicVO entity = handlerBranchBasicList(event.getBranchName(), version, event, app);
                if (null != entity) {
                    entityList.add(entity);
                }
                if (StringUtil.isEmpty(event.getReleaseBranchName())) {
                    continue;
                }
                CoverageBranchBasicVO releaseEntity = handlerBranchBasicList(event.getReleaseBranchName(), version, event, app);
                if (null != releaseEntity) {
                    entityList.add(releaseEntity);
                }
            }
        }
        if (CollectionUtil.isNotEmpty(entityList)) {
            coverageRepository.batchInsertBranchBasic(entityList);
        }
    }

    public void updateCoverageReason(EditCoverageReasonCommand command) {
        CoverageReasonEditEvent reasonEditEvent = coverageConverter.converter(command);
        reasonEditEvent.setAggregateId(command.getAggregateId());
        coverageRepository.updateBatch(reasonEditEvent);
        //标记覆盖率确认状态
        setCoverageCheckFlag(command);
        apply(reasonEditEvent);
    }

    private void setCoverageCheckFlag(EditCoverageReasonCommand command) {
        log.info("setCoverageCheckFlag_command:{}", JsonUtil.toJSON(command));
        if (Objects.isNull(command) || CollectionUtils.isEmpty(command.getCoverageReasonVOS())) {
            return;
        }
        Set<String> versionCodeSet = command.getCoverageReasonVOS().stream()
                .map(CoverageReasonVO::getVersionCode).filter(StringUtil::isNotBlank).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(versionCodeSet)) {
            tmTestPlanRepository.updateCheckFlag("", versionCodeSet, Boolean.TRUE, command.getTransactor());
        }
    }

    private CoverageBranchBasicVO handlerBranchBasicList(String branchName, CreateBranchQcEvent.Version version, CreateBranchQcEvent event, BaseApplicationDO app) {
        CoverageBranchBasicVO entity = new CoverageBranchBasicVO();
        entity.setVersionCode(version.getCode());
        entity.setBranchName(branchName);
        entity.setVersionName(version.getName());
        entity.setBasicBranchName(StringUtil.isNotEmpty(event.getBasicBranchName()) ? event.getBasicBranchName() : BranchEnum.MASTER.getValue());
        entity.setCreator(event.getTransactor().getUserName());
        entity.setCreatorId(event.getTransactor().getUserId());
        entity.setModifier(event.getTransactor().getUserName());
        entity.setModifierId(event.getTransactor().getUserId());
        entity.setGitProjectId(app.getGitProjectId());
        entity.setAppId(app.getAppId());
        int count = coverageRepository.selectBranchBasicCount(new CoverageBranchBasicVO(entity.getVersionCode(), app.getAppId(), entity.getBranchName()));
        if (count > 0) {
            return null;
        }
        String commitId = gitlabService.getBranchCommitId(app.getGitProjectId().intValue(), entity.getBasicBranchName());
        if (StringUtils.isNotEmpty(commitId)) {
            entity.setBasicCommitId(commitId);
        } else {
            log.error("获取分支commit失败：appId:{}, gitProjectId:{}, branchName:{}", app.getAppId(), app.getGitProjectId(), entity.getBasicBranchName());
        }
        return entity;
    }

    private List<CoverageRecordBasicVO> buildNewList(List<BaseApplicationDO> baseApplicationDOS, CoverageRecordBasicVO entity, String releaseBranchName) {
        List<CoverageRecordBasicVO> newList = new ArrayList<>();
        if (CollectionUtil.isEmpty(baseApplicationDOS)) {
            return newList;
        }
        for (BaseApplicationDO baseApplicationDO : baseApplicationDOS) {
            Boolean isWhiteList = false;
            BigDecimal standardRate = new BigDecimal(60);
            try {
                ApplicationVO applicationVO = getApplicationDetail(baseApplicationDO.getAppId(), baseApplicationDO.getProductCode());
                if (null != applicationVO && null != applicationVO.getWhiteList()) {
                    isWhiteList = applicationVO.getWhiteList();
                    standardRate = applicationVO.getCoverageStandardValue();
                }
            } catch (Exception e) {
                log.error("获取应用信息异常，appId : {}", baseApplicationDO.getAppId(), e);
            }
            VersionVO versionVO = new VersionVO();
            try {
                versionVO = getVersionInfo(entity.getVersionCode());
            } catch (Exception e) {
                log.error("获取版本信息异常，versionCode : {}", entity.getVersionCode(), e);
            }
            Long deptId = 0L;
            String deptName = "";
            try {
                SimpleQueryVO productVO = getProductVO(baseApplicationDO.getProductCode());
                if (null != productVO) {
                    deptId = productVO.getDeptId();
                    deptName = productVO.getDeptName();
                }
            } catch (Exception e) {
                log.error("获取产品、部门异常，productCode : {}", entity.getProductCode(), e);
            }
            newList.add(new CoverageRecordBasicVO(baseApplicationDO.getAppId(), baseApplicationDO.getName(), deptId, deptName, baseApplicationDO.getProductCode(), baseApplicationDO.getProductName(), entity.getVersionCode(),
                    versionVO.getName(), entity.getBranchName(), RecordTypeEnum.FEATURE, RecordStatusEnum.INITIAL, isWhiteList, versionVO.getTestStrategy(), entity.getAppType(), standardRate));
            if (StringUtil.isEmpty(releaseBranchName)) {
                newList.add(new CoverageRecordBasicVO(baseApplicationDO.getAppId(), baseApplicationDO.getName(), deptId, deptName, baseApplicationDO.getProductCode(), baseApplicationDO.getProductName(), entity.getVersionCode(),
                        versionVO.getName(), entity.getBranchName(), RecordTypeEnum.BRANCH, RecordStatusEnum.INITIAL, isWhiteList, versionVO.getTestStrategy(), entity.getAppType(), standardRate));
                newList.add(new CoverageRecordBasicVO(baseApplicationDO.getAppId(), baseApplicationDO.getName(), deptId, deptName, baseApplicationDO.getProductCode(), baseApplicationDO.getProductName(), entity.getVersionCode(),
                        versionVO.getName(), BranchEnum.PROD.getValue(), RecordTypeEnum.MASTER, RecordStatusEnum.INITIAL, isWhiteList, versionVO.getTestStrategy(), entity.getAppType(), standardRate));
            } else {
                newList.add(new CoverageRecordBasicVO(baseApplicationDO.getAppId(), baseApplicationDO.getName(), deptId, deptName, baseApplicationDO.getProductCode(), baseApplicationDO.getProductName(), entity.getVersionCode(),
                        versionVO.getName(), releaseBranchName, RecordTypeEnum.BRANCH, RecordStatusEnum.INITIAL, isWhiteList, versionVO.getTestStrategy(), entity.getAppType(), standardRate));
                newList.add(new CoverageRecordBasicVO(baseApplicationDO.getAppId(), baseApplicationDO.getName(), deptId, deptName, baseApplicationDO.getProductCode(), baseApplicationDO.getProductName(), entity.getVersionCode(),
                        versionVO.getName(), releaseBranchName, RecordTypeEnum.MASTER, RecordStatusEnum.INITIAL, isWhiteList, versionVO.getTestStrategy(), entity.getAppType(), standardRate));
            }
        }
        return newList;
    }

    /**
     * 组装ServiceName
     *
     * @param jacocoInstanceVOS
     * @return
     */
    private String getServiceName(List<JacocoInstanceVO> jacocoInstanceVOS) {
        StringBuilder sb = new StringBuilder();
        boolean comma = false;
        for (JacocoInstanceVO jacocoInstanceVO : jacocoInstanceVOS) {
            if (comma) {
                sb.append(",");
            } else {
                comma = true;
            }
            if (jacocoInstanceVO.getType() == 1) {
                sb.append(jacocoInstanceVO.getIp());
            } else if (jacocoInstanceVO.getType() == 2) {
                sb.append(jacocoInstanceVO.getName());
            }
        }
        return sb.toString();
    }

    /**
     * 通过pipeline查询podIp集合
     *
     * @param entity {@link CoveragePublishVO}
     * @return podIp集合
     */
    private List<String> getPodIpList(CoveragePublishVO entity) {
        List<String> instanceNameList = Arrays.asList(entity.getServiceName().split(","));
        if (CollectionUtil.isEmpty(instanceNameList)) {
            log.info(entity.getId() + "_getPodIpList_instanceNameList_is_null!");
            return new ArrayList<>();
        }
        Set<String> resultList = new HashSet<>(instanceNameList.size());
        List<String> toQueryList = new ArrayList<>();
        instanceNameList.forEach(name -> {
            if (StringUtil.isNotBlank(name)) {
                if (isIpAddress(name)) {
                    resultList.add(name);
                } else {
                    toQueryList.add(name.contains(".") ? Arrays.asList(name.split("\\.")).get(0) : name);
                }
            }
        });
        log.info(entity.getId() + "_getPodIpList_toQueryList->{}", JsonUtil.toJSON(toQueryList));
        if (CollectionUtil.isNotEmpty(toQueryList)) {
            resultList.addAll(getPodIpListByInstanceNameList(toQueryList));
        }
        log.info(entity.getId() + "_getPodIpList_resultList->{}", JsonUtil.toJSON(resultList));
        return new ArrayList<>(resultList);
    }

    /**
     * 根据实例名称查询podIp集合
     *
     * @param instanceNameList 实例名称集合
     * @return podIp集合
     */
    private List<String> getPodIpListByInstanceNameList(List<String> instanceNameList) {
        List<String> resultList = new ArrayList<>();
        try {
            resultList = pipelineRpcService.getPodIpListByInstanceNameList(instanceNameList);
        } catch (Exception e) {
            log.error("getPodIpListByInstanceNameList_instanceName->{},_e->{}", instanceNameList, e.getMessage());
        }
        return resultList;
    }

    /**
     * 生成并保存exec文件
     *
     * @param ipList
     * @param entity
     * @return
     * @throws IOException
     */
    private boolean generateExec(final List<String> ipList, final CoveragePublishVO entity, final CoveragePublishQuery query, Logger logger, DiffTypeEnum diffType) {
        try {
            logger.info("检查容器启动前的参数：" + entity.toString());
            String branchName = entity.getBranchName().replaceAll("/", "");
            String publicPath = entity.getVersionCode() + "-" + entity.getBranchName() + "-" + entity.getAppId() + "-" + "exec" + "/" + entity.getAppId() + "/" + branchName;
            StringBuilder localExecPath = new StringBuilder();
            localExecPath.append(LOCAL_FILE_PATH).append(publicPath).append("/").append(entity.getCommitId()).append("/");
            StringBuilder remoteExecPath = new StringBuilder();
            remoteExecPath.append(publicPath).append("/").append(entity.getCommitId()).append("/");
            File file = new File(localExecPath.toString());
            if (!file.exists() && !file.isDirectory()) {
                file.mkdirs();
            }
            int failureCount = 0;
            for (int i = 0; i < ipList.size(); i++) {
                String execName = new SimpleDateFormat("yyyyMMddhhmmssSSS").format(new Date()) + "jacoco.exec";
                String execFullPath = localExecPath + execName;
                FileOutputStream localFile = null;
                try {
                    if (!checkRepeatData(entity.getVersionCode() + "-" + entity.getBranchName() + "-" + entity.getAppId() + "-" + ipList.get(i), ipList.get(i))) {
                        continue;
                    }
                    localFile = new FileOutputStream(execFullPath);
                    jacocoService.socketConnect(localFile, entity, ipList.get(i));
                    log.info("开始上传oss.版本号：{}，ip:{}", entity.getVersionName(), ipList.get(i));
                    boolean flag = ossService.createObject(BucketEnum.COVERAGE_EXEC_BUCKET.getValue(), remoteExecPath.toString(), execName, execFullPath);
                    if (flag) {
                        logger.info("第" + (i + 1) + "个ip：" + ipList.get(i) + ",保存oss" + "成功！execName = " + execName);
                        if (null == entity.getFlowLaneType()) {
                            entity.setFlowLaneType(this.getFlowLaneType(entity.getVersionCode(), null));
                        }
                        CoverageExecVO coverageExecEntity = new CoverageExecVO(entity.getVersionCode(), entity.getAppId(), entity.getBranchName(), execName, remoteExecPath.toString(),
                                BucketEnum.COVERAGE_EXEC_BUCKET.getValue(), entity.getCommitId(), query.getCreator(), query.getCreatorId(), query.getCreator(), query.getCreatorId(),
                                entity.getFlowLaneType(), diffType);
                        coverageRepository.insertSelective(coverageExecEntity);
                    } else {
                        logger.warning("第" + (i + 1) + "个ip：" + ipList.get(i) + ",保存oss" + "失败！execName = " + execName);
                        failureCount++;
                    }
                } catch (Exception e) {
                    logger.warning("第" + (i + 1) + "个ip：" + ipList.get(i) + ",保存失败：" + e);
                    failureCount++;
                }
            }
            return generateResult(remoteExecPath.toString(), failureCount, ipList.size(), logger);
        } catch (Exception e) {
            log.error("generateExec 异常", e);
        } finally {
            String rmPath = LOCAL_FILE_PATH + entity.getVersionCode() + "-" + entity.getBranchName() + "-" + entity.getAppId() + "-" + "exec";
            File dir = new File(rmPath);
            boolean flag = XmlUtils.deleteDir(dir);
            if (flag) {
                log.info("删除EXEC成功！！path : {}", rmPath);
            } else {
                log.warn("删除EXEC失败！！path : {}", rmPath);
            }
        }
        return false;
    }

    /**
     * 通过版本状态获取发布泳道
     *
     * @param versionCode
     * @return
     */
    public String getFlowLaneType(String versionCode, VersionInfoVO versionInfoVO) {
        if (StringUtil.isEmpty(versionCode)) {
            throw new ServiceException("通过版本状态获取发布泳道，版本号不能为空");
        }
        if (null == versionInfoVO) {
            versionInfoVO = projectRpcService.findVersionBaseInfoQuery(versionCode);
        }
        if (null == versionInfoVO) {
            return null;
        }
        if (versionInfoVO.getStatus().equals(VersionStatus.SCHEDULING.name()) || versionInfoVO.getStatus().equals(VersionStatus.DEVELOPING.name())) {
            return FlowLaneTypeEnum.FLOW_FEATURE.name();
        } else if (VersionStatus.SMOKING.name().equals(versionInfoVO.getStatus())
                || VersionStatus.TESTING.name().equals(versionInfoVO.getStatus())
                || VersionStatus.WAIT_REGRESS.name().equals(versionInfoVO.getStatus())) {
            return FlowLaneTypeEnum.FLOW_TEST.name();
        } else if (VersionStatus.REGRESSING.name().equals(versionInfoVO.getStatus())
                || VersionStatus.REGRESSED.name().equals(versionInfoVO.getStatus())
                || VersionStatus.WAIT_AUDIT.name().equals(versionInfoVO.getStatus())
                || VersionStatus.WAIT_RELEASE.name().equals(versionInfoVO.getStatus())
                || VersionStatus.RELEASING.name().equals(versionInfoVO.getStatus())
                || VersionStatus.ACCEPTING.name().equals(versionInfoVO.getStatus())) {
            return FlowLaneTypeEnum.FLOW_PROD.name();
        } else {
            return null;
        }
    }

    private boolean checkRepeatData(String key, String value) {
        if (!redisTemplate.hasKey(key)) {
            redisTemplate.setKey(key, value, 10L, TimeUnit.SECONDS);
            return true;
        } else {
            log.info("Key[{}] is exist.Try again later.", key);
            return false;
        }
    }

    /**
     * 判断是否是ip地址
     */
    public boolean isIpAddress(String serviceName) {
        Pattern pattern = Pattern.compile("^((\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5]" + "|[*])\\" + ".){3}" + "(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5]|[*])$");
        return pattern.matcher(serviceName).matches() ? true : false;
    }

    public List<String> splitToList(List<String> inputList, String delimiter) {
        List<String> result = new ArrayList<>();
        for (String input : inputList) {
            String name = input.split(delimiter)[0];
            result.addAll(Arrays.asList(name));
        }
        return result;
    }

    /**
     * 获取产品、部门信息
     *
     * @param productCode
     * @return
     */
    public SimpleQueryVO getProductVO(String productCode) {
        if (StringUtil.isEmpty(productCode)) {
            return null;
        }
        return productRpcService.getProductVO(productCode);
    }

    /**
     * 获取应用信息
     *
     * @param appId
     * @return
     */
    public ApplicationVO getApplicationDetail(String appId, String productCode) {
        final PageApplicationQuery query = new PageApplicationQuery();
        query.setAppId(appId);
        query.setProductCode(productCode);
        query.setNeedPermissions(Boolean.FALSE);
        final PageApplicationVO detailVO = pipelineRpcService.getPageApplicationVO(query);
        if (null == detailVO || detailVO.getTotal() == 0) {
            return null;
        }
        List<ApplicationVO> applicationVOS = detailVO.getList();
        return applicationVOS.get(0);
    }

    public List<String> getNotWhiteListByProductCode(String productCode) {
        final PageApplicationQuery query = new PageApplicationQuery();
        query.setProductCode(productCode);
        query.setNeedPermissions(Boolean.FALSE);
        final PageApplicationVO detailVO = pipelineRpcService.getPageApplicationVO(query);
        if (null == detailVO || detailVO.getTotal() == 0) {
            log.error("获取应用信息为空，productCode : {}", productCode);
            return null;
        }
        List<ApplicationVO> voList = detailVO.getList();
        if (CollectionUtil.isEmpty(voList)) {
            return null;
        }
        List<String> appIdList = voList.stream().filter(applicationVO -> (!applicationVO.getWhiteList())).map(ApplicationVO::getAppId).collect(Collectors.toList());
        return appIdList;
    }

    /**
     * 获取版本信息
     *
     * @param versionCode
     * @return
     */
    public VersionVO getVersionInfo(String versionCode) {
        VersionVO versionVO = projectRpcService.findVersionQuery(versionCode);
        if (null == versionVO) {
            log.error("获取版本信息为空，versionCode : {}", versionCode);
            return new VersionVO();
        }
        if (StringUtil.isNotEmpty(versionVO.getType()) && versionVO.getType().equals(VersionTypeEnum.URGENT_TYPE.name())) {
            versionVO.setTestStrategy(VersionTypeEnum.URGENT_TYPE.name());
        }
        return versionVO;
    }

    /**
     * 保存覆盖率执行记录
     *
     * @param entity
     * @param applicationDetail
     * @param testStrategy
     * @param parameter
     * @return
     */
    public CoverageRecordBasicVO saveCoverageRecord(CoverageRecordBasicVO entity,
                                                    ApplicationVO applicationDetail,
                                                    String testStrategy,
                                                    CoverageRecordGenerateParameter parameter,
                                                    RecordStatusEnum status,
                                                    SimpleQueryVO productVO) {
        CoverageRecordBasicVO basicEntity = setCoverageRecordBasicEntity(entity, applicationDetail, testStrategy, parameter, productVO);
        if (basicEntity.getStatus().equals(RecordStatusEnum.INITIAL) && !basicEntity.getDiffType().equals(DiffTypeEnum.FULL)) {
            basicEntity.setStatus(status);
            basicEntity.setEnable(Boolean.TRUE);
            basicEntity.setGmtCreate(new Date());
            basicEntity.setGmtModified(new Date());
            coverageRepository.updateByPrimaryKeySelective(basicEntity);
            return basicEntity;
        } else {
            CoverageRecordBasicVO newEntity = new CoverageRecordBasicVO();
            newEntity.setDeptId(basicEntity.getDeptId());
            newEntity.setDeptName(basicEntity.getDeptName());
            newEntity.setProductCode(basicEntity.getProductCode());
            newEntity.setProductName(basicEntity.getProductName());
            newEntity.setVersionCode(basicEntity.getVersionCode());
            newEntity.setVersionName(basicEntity.getVersionName());
            newEntity.setAppId(basicEntity.getAppId());
            newEntity.setAppName(basicEntity.getAppName());
            newEntity.setBranchName(basicEntity.getBranchName());
            newEntity.setAppType(basicEntity.getAppType());
            newEntity.setRecordType(parameter.getRecordType());
            newEntity.setDiffType(parameter.getDiffType() != null ? parameter.getDiffType() : DiffTypeEnum.INCREMENT);
            newEntity.setStatus(status);
            newEntity.setEnable(Boolean.TRUE);
            newEntity.setRecordUrl(basicEntity.getRecordUrl());
            newEntity.setStandardRate(basicEntity.getStandardRate());
            newEntity.setIsWhiteList(basicEntity.getIsWhiteList());
            newEntity.setTestStrategy(basicEntity.getTestStrategy());
            newEntity.setRecordErrorMsg(basicEntity.getRecordErrorMsg());
            newEntity.setRemark(basicEntity.getRemark());
            newEntity.setComment(basicEntity.getComment());
            newEntity.setGenerateType(parameter.getGenerateType());
            newEntity.setCreator(parameter.getCreator());
            newEntity.setCreatorId(parameter.getCreatorId());
            newEntity.setModifier(parameter.getCreator());
            newEntity.setModifierId(parameter.getCreatorId());
            newEntity.setTaskId(parameter.getTaskId());
            newEntity.setEnvName(parameter.getEnvName());
            newEntity.setFlowLaneType(basicEntity.getFlowLaneType());
            newEntity = coverageRepository.insertCoverageRecordBasic(newEntity);
            return newEntity;
        }
    }

    public String uploadErrorLog(CoverageRecordBasicVO entity, String localLogFile, Logger logger) {
        String branchName = entity.getBranchName().replaceAll("/", "");
        String fileName = new SimpleDateFormat("yyyyMMddhhmmssSSS").format(new Date()) + ".log";
        String ossLogPath = entity.getVersionCode() + "/" + branchName + "/" + entity.getAppId() + "/log/";
        String recordUrl = config.getAmazonS3Config().getEndPoint() + "/" + BucketEnum.COVERAGE_LOG_BUCKET.getValue() + "/" + ossLogPath + fileName;
        try {
            boolean flag = ossService.createObject(BucketEnum.COVERAGE_LOG_BUCKET.getValue(), ossLogPath, fileName, localLogFile);
            logger.info("上传文件到OSS文件服务 ：" + flag);
        } catch (Exception e) {
            logger.warning("上传文件到OSS文件服务异常。recordUrl = " + recordUrl);
            return "";
        }
        return recordUrl;
    }

    /**
     * 设置CoverageRecordBasicEntity
     *
     * @param entity
     * @param applicationDetail
     * @param testStrategy
     * @param parameter
     * @return
     */
    public CoverageRecordBasicVO setCoverageRecordBasicEntity(CoverageRecordBasicVO entity, ApplicationVO applicationDetail, String testStrategy, CoverageRecordGenerateParameter parameter,
                                                              SimpleQueryVO productVO) {
        if (null == applicationDetail) {
            entity.setStandardRate(new BigDecimal(60.00));
            entity.setIsWhiteList(Boolean.FALSE);
        } else {
            entity.setStandardRate(applicationDetail.getCoverageStandardValue() != null ? applicationDetail.getCoverageStandardValue() : new BigDecimal(60.00));
            entity.setIsWhiteList(applicationDetail.getWhiteList() != null ? applicationDetail.getWhiteList() : Boolean.FALSE);
        }
        if (null != productVO) {
            entity.setDeptId(productVO.getDeptId());
            entity.setDeptName(productVO.getDeptName());
            entity.setProductCode(productVO.getProductCode());
            entity.setProductName(productVO.getProductName());
        }
        if (StringUtil.isNotEmpty(testStrategy)) {
            entity.setTestStrategy(testStrategy);
        }
        entity.setVersionName(parameter.getVersionName());
        entity.setGenerateType(parameter.getGenerateType());
        entity.setCreator(parameter.getCreator());
        entity.setCreatorId(parameter.getCreatorId());
        entity.setModifier(parameter.getCreator());
        entity.setModifierId(parameter.getCreatorId());
        entity.setDiffType(parameter.getDiffType() != null ? parameter.getDiffType() : DiffTypeEnum.INCREMENT);
        entity.setTaskId(parameter.getTaskId());
        return entity;
    }

    /**
     * 创建执行日志
     *
     * @param localLogFile
     * @param localFilePath
     * @return
     */
    public FileHandler createExecuteLog(String localLogFile, String localFilePath) {
        FileHandler fh = null;
        try {
            File file = new File(localLogFile);
            File path = new File(localFilePath);
            if (!path.exists()) {
                path.mkdirs();
            }
            if (!file.exists()) {
                file.createNewFile();
            }
            fh = new FileHandler(localLogFile);
            logger.addHandler(fh);
            fh.setFormatter(new SimpleFormatter());
        } catch (IOException e) {
            log.error("创建日志文件异常。localLogFile ：{}", localLogFile, e);
        }
        return fh;
    }

    /**
     * 判断生成exec结果
     *
     * @param remoteExecPath
     * @param failureCount
     * @param ipSize
     * @return
     */
    private boolean generateResult(String remoteExecPath, int failureCount, int ipSize, Logger logger) {
        if (failureCount > 0) {
            if (failureCount < ipSize) {
                logger.warning("部分exec文件生成成功，exec文件路径:" + remoteExecPath);
                return true;
            } else {
                logger.warning("全部exec文件生成失败！");
                return false;
            }
        } else {
            logger.info("全部exec文件生成成功，exec文件路径:" + remoteExecPath);
            return true;
        }
    }

    /**
     * 求两个对象List的交集
     *
     * @param oldArrayList
     * @param newArrayList
     * @return
     */
    private List<CoverageRecordBasicVO> sameList(List<CoverageRecordBasicVO> oldArrayList, List<CoverageRecordBasicVO> newArrayList) {
        List<CoverageRecordBasicVO> resultList =
                newArrayList.stream().filter(item -> oldArrayList.stream().map(e -> e.getVersionCode() + "&" + e.getAppId() + "&" + e.getBranchName()).collect(Collectors.toList()).contains(item.getVersionCode() + "&" + item.getAppId() + "&" + item.getBranchName())).collect(Collectors.toList());
        return resultList;
    }

    /**
     * 求两个对象List的差集(多属性比对)
     *
     * @param firstArrayList
     * @param secondArrayList
     * @return
     */
    private List<CoverageRecordBasicVO> diffList(List<CoverageRecordBasicVO> firstArrayList, List<CoverageRecordBasicVO> secondArrayList) {
        List<CoverageRecordBasicVO> resultList =
                firstArrayList.stream().filter(item ->
                                !secondArrayList.stream()
                                        .map(e -> e.getVersionCode() + "&" + e.getAppId() + "&" + e.getBranchName()).collect(Collectors.toList())
                                        .contains(item.getVersionCode() + "&" + item.getAppId() + "&" + item.getBranchName()))
                        .collect(Collectors.toList());
        return resultList;
    }

    /**
     * 切换覆盖率报告类型
     *
     * @param entity
     */
    private void switchCoverageRecordType(final CoverageRecordBasicVO entity, FileHandler fh, String localLogFile, Logger logger) {
        String appType = entity.getAppType().name();
        if (StringUtil.isEmpty(appType)) {
            logger.warning("应用类型为空，versionCode : " + entity.getVersionCode());
            uploadFileAndUpdateRecord(localLogFile, entity, RecordStatusEnum.FAIL, RecordErrorMsgEnum.APP_RECORD_TYPE_IS_EMPTY.getValue(), new CoverageRecordGenerateVO(), logger);
            return;
        }
        switch (appType) {
            case "JAVA":
                //Jacoco代码覆盖率
                try {
                    generateJacocoCoverageReport(entity, fh, localLogFile, logger);
                } catch (Exception e) {
                    log.error("生成覆盖率异常", e);
                }
            case "WEB":
                //前端代码覆盖率
            case "GOLANG":
                //go语言代码覆盖率
            default:
                break;
        }
    }

    private void startGenerateReport(final CoverageRecordBasicVO entity, final CoverageRecordGenerateParameter parameter, Logger logger, String localLogFile, FileHandler fh) {
        try {
            String hostAddress = "";
            try {
                InetAddress ipAddress = Inet4Address.getLocalHost();
                hostAddress = ipAddress.getHostAddress();
            } catch (Exception e) {
                log.info(">>>>>>获取本地ip异常。", e);
            }
            logger.info("开始初始化准备, ID = " + entity.getId() + ". 生成泳道 ：" + entity.getFlowLaneType().getValue() + ". 执行机地址 : " + hostAddress);
            if (null != parameter.getDiffType() && parameter.getDiffType().equals(DiffTypeEnum.FULL)) {
                entity.setDiffType(parameter.getDiffType());
            }
            coverageRepository.delCoverageRecord(entity);
            SimpleQueryVO productVO = null;
            ApplicationVO applicationDetail = null;
            VersionVO versionVO = new VersionVO();
            try {
                if (null == entity.getDeptId() || entity.getDeptId() == 0L) {
                    productVO = getProductVO(parameter.getProductCode());
                    if (null == productVO) {
                        logger.warning("获取产品、部门信息为空，productCode : " + entity.getProductCode());
                        entity.setRecordErrorMsg(RecordErrorMsgEnum.GET_PRODUCT_INFO_EMPTY.getValue());
                        String recordUrl = uploadErrorLog(entity, localLogFile, logger);
                        entity.setRecordUrl(recordUrl);
                        saveCoverageRecord(entity, null, null, parameter, RecordStatusEnum.INITIAL, null);
                        return;
                    }
                }
            } catch (Exception e) {
                logger.warning("获取产品、部门信息异常，productCode : " + entity.getProductCode());
                entity.setRecordErrorMsg(RecordErrorMsgEnum.GET_PRODUCT_INFO_ERROR.getValue());
                String recordUrl = uploadErrorLog(entity, localLogFile, logger);
                entity.setRecordUrl(recordUrl);
                saveCoverageRecord(entity, null, null, parameter, RecordStatusEnum.FAIL, null);
                return;
            }
            try {
                applicationDetail = getApplicationDetail(entity.getAppId(), parameter.getProductCode());
                if (null == applicationDetail) {
                    logger.warning("获取应用信息为空，appId : " + entity.getAppId());
                    entity.setRecordErrorMsg(RecordErrorMsgEnum.GET_APP_INFO_EMPTY.getValue());
                    String recordUrl = uploadErrorLog(entity, localLogFile, logger);
                    entity.setRecordUrl(recordUrl);
                    saveCoverageRecord(entity, null, null, parameter, RecordStatusEnum.INITIAL, productVO);
                    return;
                }
                if (applicationDetail.getWhiteList()) {
                    logger.info("白名单应用无需生产覆盖率报告");
                    entity.setRecordErrorMsg(RecordErrorMsgEnum.WHITE_LIST_APP.getValue());
                    entity.setRecordUrl("");
//                    entity.setRemark(RecordRemarkEnum.WHITE_LIST_APP.getValue());
                    entity.addBasicVORemark(RecordRemarkEnum.WHITE_LIST_APP.getValue());
                    saveCoverageRecord(entity, applicationDetail, null, parameter, RecordStatusEnum.NEEDLESS, productVO);
                    return;
                }
            } catch (Exception e) {
                logger.warning("获取应用信息异常，appId : " + entity.getAppId() + "，" + e.getMessage());
                entity.setRecordErrorMsg(RecordErrorMsgEnum.GET_APP_INFO_ERROR.getValue());
                String recordUrl = uploadErrorLog(entity, localLogFile, logger);
                entity.setRecordUrl(recordUrl);
                saveCoverageRecord(entity, applicationDetail, null, parameter, RecordStatusEnum.FAIL, productVO);
                return;
            }
            try {
                versionVO = getVersionInfo(parameter.getVersionCode());
                if (StringUtil.isEmpty(versionVO.getTestStrategy())) {
                    logger.warning("获取版本信息为空，versionCode : " + parameter.getVersionCode());
                    entity.setRecordErrorMsg(RecordErrorMsgEnum.GET_TEST_STRATEGY_EMPTY.getValue());
                    String recordUrl = uploadErrorLog(entity, localLogFile, logger);
                    entity.setRecordUrl(recordUrl);
                    saveCoverageRecord(entity, applicationDetail, versionVO.getTestStrategy(), parameter, RecordStatusEnum.INITIAL, productVO);
                    return;
                }
            } catch (Exception e) {
                logger.warning("获取版本信息异常，versionCode : " + parameter.getVersionCode() + ", msg : " + e.getMessage());
                e.printStackTrace();
                entity.setRecordErrorMsg(RecordErrorMsgEnum.GET_TEST_STRATEGY_ERROR.getValue());
                String recordUrl = uploadErrorLog(entity, localLogFile, logger);
                entity.setRecordUrl(recordUrl);
                saveCoverageRecord(entity, applicationDetail, versionVO.getTestStrategy(), parameter, RecordStatusEnum.FAIL, productVO);
                return;
            }
            entity.setRecordErrorMsg("");
            entity.setRecordUrl("");
            final CoverageRecordBasicVO newEntity = saveCoverageRecord(entity, applicationDetail, versionVO.getTestStrategy(), parameter, RecordStatusEnum.INITIAL, productVO);
            switchCoverageRecordType(newEntity, fh, localLogFile, logger);
        } catch (ServiceException e) {
            log.error("生成报告业务异常。", e);
        } catch (Exception e) {
            log.error("生成报告异常。", e);
        } finally {
            deleteCache(fh, entity, parameter);
            sendCoverageResultMessage(parameter);
        }
    }

    /**
     * 判断class和源码文件是否存在
     */
    private Boolean judgeClassSourceFileExit(String versionCode, String branchName, String appId, String commitId) {
        String classesPath = LOCAL_FILE_PATH + versionCode + "-" + branchName + "-" + appId + "/" + branchName + "/" + commitId +
                "/classes";
        String sourcesPath = LOCAL_FILE_PATH + versionCode + "-" + branchName + "-" + appId + "/" + branchName + "/" + commitId +
                "/sources";
        File classesFile = new File(classesPath);
        File sourcesFile = new File(sourcesPath);
        if (!classesFile.exists() || !sourcesFile.exists()) {
            return false;
        }
        if (classesFile.isDirectory()) {
            String[] files = classesFile.list();
            if (files.length == 0) {
                return false;
            }
        }
        if (sourcesFile.isDirectory()) {
            String[] files = sourcesFile.list();
            if (files.length == 0) {
                return false;
            }
        }
        return true;
    }

    /**
     * 生成Jacoco覆盖率报告
     *
     * @param entity
     */
    public void generateJacocoCoverageReport(CoverageRecordBasicVO entity, FileHandler fh, String localLogFile, Logger logger) {
        logger.info("开始" + entity.getGenerateType().getValue() + "生成" + entity.getRecordType().getValue() + entity.getDiffType().getValue()
                + "覆盖率报告。" + "versionCode = " + entity.getVersionCode() + ", appId = " + entity.getAppId());
        logger.info("开始更新生成状态。状态流转：" + RecordStatusEnum.INITIAL.getValue() + "-->" + RecordStatusEnum.RUNNING.getValue());
        coverageRepository.updateCoverageRecordById(entity.getId(), RecordStatusEnum.RUNNING.name(), "", "", "", "", "", null,
                entity.getRemark(), null, null, "", "", "", "");
        entity.setStatus(RecordStatusEnum.RUNNING);
        // 同步覆盖率状态-生成中
        CoverageRecordPageQuery coverageRecordPageQuery = new CoverageRecordPageQuery();
        coverageRecordPageQuery.setVersionCode(entity.getVersionCode());
        coverageRecordPageQuery.setAppId(entity.getAppId());
        CoverageRecordVO coverageRecordVO = coverageRepository.getCoverageRecordList(coverageRecordPageQuery).get(0);
        CoverageStatusVO coverageStatusVO = coverageConverter.converter(coverageRecordVO);
        queryVersionCoverageRate(coverageStatusVO);
        reactiveEmitter.emit(TestEventEnums.TEST_CODE_COVER_GENERATE.toReactiveId(entity.getVersionCode()), coverageStatusVO);
        CoverageRecordGenerateVO generateEntity = new CoverageRecordGenerateVO();
        try {
            generateEntity = setGenerateEntity(entity, logger);
        } catch (ServiceException e) {
            log.error(e.getMsg(), e);
            logger.warning(e.getMsg());
            RecordStatusEnum statusEnum = RecordStatusEnum.FAIL;
            if (e.getMsg().equals(RecordErrorMsgEnum.APP_PUBLISH_RECORD_EMPTY.getValue())
                    || e.getMsg().equals(RecordErrorMsgEnum.NOT_PUBLISHED_TEST_ENV.getValue())) {
                statusEnum = RecordStatusEnum.INITIAL;
            }
            uploadFileAndUpdateRecord(localLogFile, entity, statusEnum, e.getMsg().length() <= 255 ? e.getMsg() : e.getMsg().substring(0, 254), generateEntity, logger);
            fh.close();
            return;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            logger.warning(e.getMessage());
            uploadFileAndUpdateRecord(localLogFile, entity, RecordStatusEnum.FAIL, e.getMessage().length() <= 255 ? e.getMessage() : e.getMessage().substring(0, 254), generateEntity, logger);
            fh.close();
            return;
        }
        String filterUnComparedProducts = config.getCoverageConfig().getFilterUnComparedProducts();
        if ((StringUtil.isEmpty(filterUnComparedProducts) || !Arrays.stream(filterUnComparedProducts.split(",")).anyMatch(obj -> obj.equals(entity.getProductCode())))) {
            try {
                if (!gitlabService.targetContainSource(generateEntity.getGitProjectId().intValue(), generateEntity.getBasicBranchName(), generateEntity.getBranchName())) {
                    logger.warning("发布分支落后于master分支，请先合并代码部署后，重新生成覆盖率报告");
                    uploadFileAndUpdateRecord(localLogFile, entity, RecordStatusEnum.INITIAL, RecordErrorMsgEnum.RELEASE_BEHIND_MASTER.getValue(), generateEntity, logger);
                    fh.close();
                    return;
                }
            } catch (Exception e) {
                log.error("检测目标分支是否包含源分支异常", e);
            }
            logger.info("检测当前分支是否部署过最新commitId 开始。branchName ：" + generateEntity.getBranchName());
            try {
                boolean isDeployed = isDeployedLatestCommitId(generateEntity);
                if (!isDeployed) {
                    logger.warning("当前分支未部署过最新commitId，请部署后，重新生成覆盖率报告");
                    uploadFileAndUpdateRecord(localLogFile, entity, RecordStatusEnum.INITIAL, RecordErrorMsgEnum.COMMIT_ID_NOT_DEPLOYED.getValue(), generateEntity, logger);
                    fh.close();
                    return;
                }
            } catch (Exception e) {
                log.error("检测当前分支是否部署过最新commitId异常", e);
            }
            logger.info("检测当前分支是否部署过最新commitId 结束。branchName ：" + generateEntity.getBranchName());
        }
        if (generateEntity.getCommitId().equals(generateEntity.getFirstBranchCommitId())) {
            logger.warning("commitId一样，无需对比。请检查此应用是否有代码改动。");
            uploadFileAndUpdateRecord(localLogFile, entity, RecordStatusEnum.NEEDLESS, RecordErrorMsgEnum.COMMIT_ID_IS_IDENTICAL.getValue(), generateEntity, logger);
            fh.close();
            return;
        }
        if ((RecordTypeEnum.BRANCH.equals(entity.getRecordType())) && StringUtils.isEmpty(generateEntity.getBasicCommitId())) {
            logger.warning("生成代码覆盖率失败：未查询到master基准分支部署记录");
            uploadFileAndUpdateRecord(localLogFile, entity, RecordStatusEnum.INITIAL, RecordErrorMsgEnum.BASIC_COMMIT_ID_IS_EMPTY.getValue(), generateEntity, logger);
            fh.close();
            return;
        }
        try {
            CoveragePublishVO coveragePublishVO = new CoveragePublishVO();
            coveragePublishVO.setVersionCode(entity.getVersionCode());
            coveragePublishVO.setBranchName(entity.getBranchName());
            coveragePublishVO.setAppId(entity.getAppId());
            coveragePublishVO.setFlowLaneType(generateEntity.getFlowLaneType());
            CoveragePublishQuery query = new CoveragePublishQuery();
            query.setCreatorId(entity.getCreatorId());
            query.setCreator(entity.getCreator());
            generateCoverageExec(coveragePublishVO, query, logger, entity.getDiffType());
        } catch (ServiceException e) {
            logger.warning(e.getMsg());
            uploadFileAndUpdateRecord(localLogFile, entity, RecordStatusEnum.FAIL, e.getMsg().length() <= 255 ? e.getMsg() : e.getMsg().substring(0, 254), generateEntity, logger);
            fh.close();
            return;
        }
        try {
            generateReport(generateEntity, logger);
        } catch (ServiceException e) {
            logger.warning(e.getMsg());
            RecordStatusEnum recordStatus = RecordStatusEnum.FAIL;
            String errorMsg = e.getMsg().length() <= 255 ? e.getMsg() : e.getMsg().substring(0, 254);
            if (errorMsg.contains(RecordErrorMsgEnum.NO_IMPLEMENTATION_CLASS.getValue())
                    || errorMsg.contains(RecordErrorMsgEnum.COMMIT_ID_IS_IDENTICAL.getValue())) {
                recordStatus = RecordStatusEnum.NEEDLESS;
                errorMsg = errorMsg.replaceAll("生成报告失败。", "");
            }
            uploadFileAndUpdateRecord(localLogFile, entity, recordStatus, errorMsg, generateEntity, logger);
            fh.close();
            return;
        }
        uploadFileAndUpdateRecord(localLogFile, entity, RecordStatusEnum.SUCCEED, "", generateEntity, logger);
        fh.close();
    }

    /**
     * 设置生成报告实体
     *
     * @param entity
     * @return
     */
    public CoverageRecordGenerateVO setGenerateEntity(CoverageRecordBasicVO entity, Logger logger) {
        CoverageRecordGenerateVO generateEntity = coverageRepository.selectCoverageRecords(entity);
        if (null == generateEntity) {
            throw new ServiceException(RecordErrorMsgEnum.APP_PUBLISH_RECORD_EMPTY.getValue());
        }
        if (null == generateEntity.getFlowLaneType() && null == entity.getFlowLaneType()) {
            throw new ServiceException("当前版本发布泳道为空。versionCode = " + entity.getVersionCode());
        }
        generateEntity.setFlowLaneType(entity.getFlowLaneType().name());
        generateEntity.setDiffType(entity.getDiffType());
        boolean isModules = Boolean.FALSE;
        String modules = config.getCoverageConfig().getModules();
        String excludeApps = config.getCoverageConfig().getExcludeApps();
        log.info(generateEntity.getProductCode() + "_multiModule_1648_modules:{}_excludeApps:{}", modules, excludeApps);
        if ((StringUtil.isNotEmpty(modules) && Arrays.stream(modules.split(",")).anyMatch(obj -> obj.equals(generateEntity.getProductCode())))
                && (StringUtil.isEmpty(excludeApps) || !Arrays.stream(excludeApps.split(",")).anyMatch(obj -> obj.equals(generateEntity.getAppId())))) {
            isModules = Boolean.TRUE;
        }
        log.info(generateEntity.getProductCode() + "_multiModule_1653_{}", isModules);
        buildGenerateVOPName(generateEntity, entity.getBranchName());
        buildGenerateVODownloadUrl(generateEntity, isModules);
        if (entity.getDiffType().name().equals(DiffTypeEnum.FULL.name())) {
            logger.info("全量覆盖率");
            generateEntity.setMergeDump(CoverageMergeEnum.UN_MERGE.getValue());
        } else {
            logger.info("增量覆盖率，开始获取分支第一次部署记录commitId。");
            generateEntity.setMergeDump(CoverageMergeEnum.MERGE.getValue());
            String firstBranchCommitId = getFirstBranchCommitId(generateEntity);
            generateEntity.setFirstBranchCommitId(firstBranchCommitId);
        }
        generateEntity.setCreator(entity.getCreator());
        generateEntity.setCreatorId(entity.getCreatorId());
        return generateEntity;
    }

    private void buildGenerateVOPName(CoverageRecordGenerateVO generateEntity, String branchName) {
        String pName = "";
        if (StringUtil.isEmpty(generateEntity.getGitUrl())) {
            if (generateEntity.getRecordType().equals(RecordTypeEnum.FEATURE)) {
                throw new ServiceException("Git地址为空。1.请确认应用是否在[功能开发泳道]部署过[" + branchName + "];2.是否部署了TEST分支，不支持TEST分支。");
            } else if (generateEntity.getRecordType().equals(RecordTypeEnum.BRANCH)) {
                throw new ServiceException(RecordErrorMsgEnum.NOT_PUBLISHED_TEST_ENV.getValue());
            } else {
                throw new ServiceException("Git地址为空。1.请确认应用是否在[回归/发布泳道]部署过[" + branchName + "]。");
            }
        }
        String[] paths = generateEntity.getGitUrl().split("/");
        if (paths.length > 0) {
            String[] names = (paths[paths.length - 1]).split("\\.");
            pName = names[0];
        }
        generateEntity.setPName(pName);
    }

    private void buildGenerateVODownloadUrl(CoverageRecordGenerateVO generateEntity, boolean isModules) {
        try {
            final FindOssFileUrlQuery query = new FindOssFileUrlQuery();
            if (isModules) {
                if (StringUtil.isEmpty(generateEntity.getOutputFileName())) {
                    throw new ServiceException("合并文件名为空异常");
                }
                query.setFileName(generateEntity.getOutputFileName());
            } else {
                if (StringUtil.isEmpty(generateEntity.getPackageName())) {
                    throw new ServiceException("下载包名为空异常");
                }
                query.setFileName(generateEntity.getPackageName());
            }
            final String downloadUrl = pipelineRpcService.downloadUrl(query);
            if (StringUtil.isEmpty(downloadUrl)) {
                log.warn("获取包下载地址为空。");
                throw new ServiceException(RecordErrorMsgEnum.PACKAGE_DOWNLOAD_URL_EMPTY.getValue());
            }
            generateEntity.setDownloadUrl(downloadUrl);
        } catch (Exception e) {
            log.error("获取包下载地址异常", e);
            throw new ServiceException(RecordErrorMsgEnum.PACKAGE_DOWNLOAD_URL_EMPTY.getValue());
        }
    }

    /**
     * 获取最早的分支部署commitId
     *
     * @param generateEntity
     */
    public String getFirstBranchCommitId(CoverageRecordGenerateVO generateEntity) {
        CoveragePublishVO entity = new CoveragePublishVO();
        entity.setAppId(generateEntity.getAppId());
        entity.setBranchName(generateEntity.getBranchName());
        entity.setCommitId(generateEntity.getCommitId());
        entity.setVersionCode(generateEntity.getVersionCode());
        CoveragePublishVO publishEntity = coverageRepository.getFirstBranchPublish(entity);
        if (null == publishEntity || StringUtil.isEmpty(publishEntity.getCommitId())) {
            return null;
        }
        return publishEntity.getCommitId();
    }

    /**
     * 上传日志文件&更新覆盖率执行记录
     *
     * @param localLogFile
     * @param entity
     * @param status
     * @param errorMessage
     */
    public void uploadFileAndUpdateRecord(String localLogFile, CoverageRecordBasicVO entity, RecordStatusEnum status, String errorMessage, CoverageRecordGenerateVO generateEntity, Logger logger) {
        String recordUrl = "";
        BigDecimal recordRate = null;
        String gitCompareUrl = "";
        int codeCoverNum = 0;
        int codeSum = 0;
        if (status.name().equals(RecordStatusEnum.FAIL.name())) {
            String branchName = entity.getBranchName().replaceAll("/", "");
            String fileName = new SimpleDateFormat("yyyyMMddhhmmssSSS").format(new Date()) + ".log";
            String ossLogPath = entity.getVersionCode() + "/" + branchName + "/" + entity.getAppId() + "/log/";
            recordUrl = config.getAmazonS3Config().getEndPoint() + "/" + BucketEnum.COVERAGE_LOG_BUCKET.getValue() + "/" + ossLogPath + fileName;
            try {
                logger.info("开始更新生成状态。状态流转：" + RecordStatusEnum.RUNNING.getValue() + "-->" + RecordStatusEnum.FAIL.getValue());
                ossService.createObject(BucketEnum.COVERAGE_LOG_BUCKET.getValue(), ossLogPath, fileName, localLogFile);
            } catch (Exception e) {
                logger.warning("上传文件到OSS文件服务异常。versionCode = " + entity.getVersionCode() + "," + "appId = " + entity.getAppId());
                coverageRepository.updateCoverageRecordById(entity.getId(), RecordStatusEnum.FAIL.name(), RecordErrorMsgEnum.UPLOAD_OSS_ERROR.getValue(), "", generateEntity.getCommitId(),
                        generateEntity.getBasicCommitId(), generateEntity.getBasicBranchName(), null, entity.getRemark(), null, null, generateEntity.getEnvName(), "", "", "");
                log.error("上传文件到OSS文件服务异常", e);
                return;
            }
        }
        if (status.name().equals(RecordStatusEnum.SUCCEED.name())) {
            recordUrl = config.getAmazonS3ConfigIntranet().getEndPoint() + generateEntity.getHtmlPath();
            try {
                if (jacocoService.isContainText(recordUrl, NONE_OF_ANALYZED_CLASSES)) {
                    String text = jacocoService.getValueByReport(recordUrl, "td.ctr2");
                    recordRate = new BigDecimal(text);
                    text = jacocoService.getValueByReport(recordUrl, "td.bar");
                    String[] cover = text.split("of");
                    if (cover.length > 1) {
                        codeCoverNum = Integer.parseInt(cover[1]) - Integer.parseInt(cover[0]);
                        codeSum = Integer.parseInt(cover[1]);
                    }
                } else {
                    entity.addBasicVORemark(RecordRemarkEnum.CODE_NOT_CLASS.getValue());
                    status = RecordStatusEnum.NEEDLESS;
                    errorMessage = RecordErrorMsgEnum.NO_IMPLEMENTATION_CLASS.getValue();
                }
            } catch (Exception e) {
                log.warn("解析覆盖率报告获取覆盖率结果异常。{}", recordUrl, e);
                errorMessage = "解析覆盖率报告获取覆盖率结果异常";
            }
            gitCompareUrl = coverageRepository.getGitCompareUrl(entity.getVersionCode(), entity.getAppId(), entity.getBranchName());
            logger.info("开始更新生成状态。状态流转：" + RecordStatusEnum.RUNNING.getValue() + "-->" + status.getValue());
            recordUrl = config.getAmazonS3Config().getEndPoint() + generateEntity.getHtmlPath();
        }
        coverageRepository.updateCoverageRecordById(entity.getId(), status.name(), errorMessage, recordUrl, generateEntity.getCommitId(), generateEntity.getBasicCommitId(),
                generateEntity.getBasicBranchName(), recordRate, entity.getRemark(), codeCoverNum, codeSum, generateEntity.getEnvName(), gitCompareUrl, generateEntity.getBucketName(),
                generateEntity.getFileName());
    }

    /**
     * 开始生成报告
     *
     * @param entity
     */
    public void generateReport(final CoverageRecordGenerateVO entity, Logger logger) {
        if (null == entity) {
            throw new ServiceException("请求实体类为空,请联系值班人员");
        }
        String branchName = entity.getBranchName().replaceAll("/", "");
        String publicPath = entity.getVersionCode() + "-" + entity.getBranchName() + "-" + entity.getAppId() + "/" + branchName;
        String localPath = LOCAL_FILE_PATH + publicPath + "/" + entity.getCommitId() + "/";
        String sourcePath = localPath + SOURCE_PATH;
        String classPath = localPath + CLASS_PATH;
        boolean isModules = Boolean.FALSE;
        String modules = config.getCoverageConfig().getModules();
        String excludeApps = config.getCoverageConfig().getExcludeApps();
        log.info(entity.getProductCode() + "_multiModule_1809_modules:{}_excludeApps:{}", modules, excludeApps);
        if ((StringUtil.isNotEmpty(modules) && Arrays.stream(modules.split(",")).anyMatch(obj -> obj.equals(entity.getProductCode())))
                && (StringUtil.isEmpty(excludeApps) || !Arrays.stream(excludeApps.split(",")).anyMatch(obj -> obj.equals(entity.getAppId())))) {
            isModules = Boolean.TRUE;
        }
        log.info(entity.getProductCode() + "_multiModule_1814_{}", isModules);
        Boolean exitTag = judgeClassSourceFileExit(entity.getVersionCode(), entity.getBranchName(), entity.getAppId(), entity.getCommitId());
        if (!exitTag) {
            log.info("sources与classes文件夹不存在，删除历史下载文件夹");
            deleteGitFileAndClassSource(entity.getVersionCode(), entity.getBranchName(), entity.getAppId(), entity.getCommitId());
            log.info("sources与classes文件夹不存在，重新下载。path : {}", localPath);
            downloadGitFile(entity, sourcePath);
            if (isModules) {
                downloadOutputFile(entity, localPath);
            } else {
                downloadClassAndSource(entity, classPath);
            }
        } else {
            if (isModules) {
                entity.setLocalClassesPath(classPath + "com/");
            } else {
                jacocoService.setLocalClassPath(entity, classPath);
            }
            log.info("versionCode : {}, branchName : {}, appId :{}, localClassesPath : {}", entity.getVersionCode(), entity.getBranchName(), entity.getAppId(), entity.getLocalClassesPath());
        }
        try {
            logger.info("源码路径：" + sourcePath);
            List<String> list = getAllFileNameAbsolutePath(sourcePath);
            LinkedHashSet<String> hashSet = new LinkedHashSet<>(list);
            List<String> parentList = new ArrayList<>(hashSet);
            if (CollectionUtil.isEmpty(parentList)) {
                throw new ServiceException("Java源码目录为空,请联系值班人员");
            } else {
                entity.setParent(parentList);
            }
        } catch (ServiceException e) {
            throw new ServiceException(e.getMsg());
        } catch (Exception e) {
            throw new ServiceException("获取源码目录异常,请联系值班人员");
        }
        try {
            executeDump(entity, publicPath, logger);
        } catch (ServiceException e) {
            throw new ServiceException(e.getMsg());
        } catch (Exception e) {
            throw new ServiceException("下载&合并exec文件异常,请联系值班人员");
        }
        try {
            completableFuture(entity, publicPath, logger, sourcePath);
        } catch (ServiceException e) {
            throw new ServiceException(e.getMsg());
        } catch (Exception e) {
            throw new ServiceException("执行jacoco代码覆盖率异常,请联系值班人员");
        }
    }

    /**
     * 递归获取jacoco解析源码时候需要的路径
     *
     * @param path 项目路径
     * @return
     */
    public static List<String> getAllFileNameAbsolutePath(String path) {
        File file = new File(path);
        File[] tempList = file.listFiles();
        List<String> list = new ArrayList<>();
        for (int i = 0; i < tempList.length; i++) {
            if (tempList[i].isFile()) {
                String absolutePath = tempList[i].getAbsolutePath();
                if (absolutePath.contains("/src/main/java")) {
                    String str1 = absolutePath.substring(0, absolutePath.indexOf("/src/main/java"));
                    if (!list.contains(str1)) {
                        list.add(str1);
                    }
                } else if (absolutePath.contains("\\src\\main\\java")) {
                    String str1 = absolutePath.substring(0, absolutePath.indexOf("\\src\\main\\java"));
                    if (!list.contains(str1)) {
                        list.add(str1);
                    }
                }
            }
            if (tempList[i].isDirectory()) {
                list.addAll(getAllFileNameAbsolutePath(tempList[i].getAbsolutePath()));
            }
        }
        return list;
    }

    /**
     * 下载class文件
     *
     * @param entity
     * @param classPath
     */
    public void downloadClassAndSource(CoverageRecordGenerateVO entity, String classPath) {
        String downloadUrl = entity.getDownloadUrl();
        if (StringUtil.isEmpty(downloadUrl)) {
            throw new ServiceException("下载包地址为空,请联系值班人员");
        }
        String fileNameGz = "";
        String fileNameJar = "";
        try {
            // 下载class包
            fileNameGz = downloadClassTarGz(classPath, downloadUrl);
            // 解压class包
            fileNameJar = decompressTarGz(classPath, fileNameGz);
            // 设置class文件路径
            jacocoService.setLocalClassPath(entity, classPath);
            log.info("versionCode : {}, branchName : {}, appId :{}, localClassesPath : {}", entity.getVersionCode(), entity.getBranchName(), entity.getAppId(), entity.getLocalClassesPath());
        } catch (ServiceException e) {
            throw new ServiceException(e.getMsg());
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        } finally {
            if (StringUtil.isNotBlank(fileNameGz)) {
                FileUtils.deleteDir(new File(classPath + fileNameGz));
            }
            if (StringUtil.isNotBlank(fileNameJar)) {
                FileUtils.deleteDir(new File(classPath + fileNameJar));
            }
        }
    }

    private String downloadClassTarGz(String classPath, String downloadUrl) {
        log.info("开始下载Class文件");
        File file = new File(classPath);
        if (!file.exists() && !file.isDirectory()) {
            file.mkdirs();
        }
        String fileName = httpService.downloadFromUrl(downloadUrl, classPath);
        if (StringUtil.isEmpty(fileName)) {
            throw new ServiceException("下载包文件失败");
        }
        log.info("下载Class文件成功！");
        return fileName;
    }

    private String downloadOutputFileZip(String localPath, String downloadUrl) {
        log.info("开始下载ZIP文件。localPath : {} , downloadUrl : {}", localPath, downloadUrl);
        File file = new File(localPath);
        if (!file.exists() && !file.isDirectory()) {
            file.mkdirs();
        }
        String fileName = httpService.downloadFromUrl(downloadUrl, localPath);
        if (StringUtil.isEmpty(fileName)) {
            throw new ServiceException("下载ZIP文件失败");
        }
        log.info("下载[{}]文件成功！downloadUrl : {}", fileName, downloadUrl);
        return fileName;
    }

    private String decompressTarGz(String classPath, String gzFileName) {
        log.info("开始解压Class文件.classPath : {}, gzFileName : {}", classPath, gzFileName);
        String jarName;
        try {
            jarName = uncompressFileUtilService.decompressTarGz(classPath + gzFileName, classPath);
            File jarFile = new File(classPath + jarName);
            File targetDir = new File(classPath);
            if (!targetDir.exists()) {
                targetDir.mkdirs();
            }
            DecompressionUtils.uncompress(jarFile, targetDir);
        } catch (ServiceException e) {
            throw new ServiceException(e.getMsg());
        } catch (Exception e) {
            log.error("Class文件下载异常", e);
            throw new ServiceException("Class文件下载异常,请稍后再试");
        }
        log.info("解压Class文件成功.classPath : {}, gzFileName : {}", classPath, gzFileName);
        return jarName;
    }

    private void setLocalClassPath(CoverageRecordGenerateVO entity, String classPath) {
        File classFile = new File(classPath + "BOOT-INF");
        if (classFile.exists()) {
            entity.setLocalClassesPath(classPath + "BOOT-INF/classes/com/");
            return;
        } else {
            File files = new File(classPath + "WEB-INF");
            if (files.exists()) {
                entity.setLocalClassesPath(classPath + "WEB-INF/classes/");
                return;
            } else {
                File file1 = new File(classPath + "com");
                if (file1.exists()) {
                    entity.setLocalClassesPath(classPath + "com/");
                    return;
                }
            }
        }
        log.info("setLocalClassPath 为空");
    }

    /**
     * 下载&合并exec文件
     *
     * @param entity
     * @param publicPath
     */
    public void executeDump(CoverageRecordGenerateVO entity, String publicPath, Logger logger) {
        String execPath = LOCAL_FILE_PATH + publicPath + "/" + entity.getCommitId() + "/exec/";
        try {
            logger.info("开始下载exec文件。");
            // 只需要当前commitId的所有exec文件
            List<CoverageExecVO> execEntityList = coverageRepository.getExecList(entity.getVersionCode(), entity.getAppId(), entity.getCommitId(), entity.getFlowLaneType(), entity.getDiffType());
            if (CollectionUtil.isEmpty(execEntityList)) {
                logger.warning("无exec文件可下载");
                throw new ServiceException("无exec文件可下载");
            }
            for (CoverageExecVO execEntity : execEntityList) {
                if (entity.getMergeDump() != null && entity.getMergeDump().equals(CoverageMergeEnum.UN_MERGE.getValue())) {
                    logger.info("无需合并历史覆盖率，仅下载最新的exec文件");
                    try {
                        ossService.downloadFile(execEntity.getBucketName(), execEntity.getExecPath() + execEntity.getExecName(), execPath);
                    } catch (Exception e) {
                        log.error("下载exec文件异常。bucketName = " + execEntity.getBucketName() + "execName = " + execEntity.getExecName() + "。" + e.getMessage());
                    }
                    break;
                } else {
                    try {
                        ossService.downloadFile(execEntity.getBucketName(), execEntity.getExecPath() + execEntity.getExecName(), execPath);
                    } catch (Exception e) {
                        log.error("下载历史exec文件异常。bucketName = " + execEntity.getBucketName() + "execName = " + execEntity.getExecName() + "。" + e.getMessage());
                    }
                }
            }
            if (entity.getMergeDump() != null && entity.getMergeDump().equals(CoverageMergeEnum.MERGE.getValue())) {
                downloadAllExec(entity, execPath, publicPath, logger);
            }
            emerge(entity, execPath, logger);
        } catch (ServiceException e) {
            log.error("下载&合并exec文件业务异常", e);
            throw new ServiceException(e.getMsg());
        } catch (Exception e) {
            log.error("下载&合并exec文件异常", e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 获取增量覆盖率中间所有的exec文件
     *
     * @param entity
     * @param execPath
     * @return
     */
    private void downloadAllExec(CoverageRecordGenerateVO entity, String execPath, String publicPath, Logger logger) {
        if (StringUtil.isEmpty(entity.getFirstBranchCommitId())) {
            logger.info("未获取到分支第一次部署记录的commitId，无需下载历史所有的exec文件");
            coverageMergeService.syncMergeSubVersionExecAndReport(entity, execPath, publicPath, logger);
            return;
        }
        String oldCommitId = entity.getFirstBranchCommitId();
        String newCommitId = entity.getCommitId();
        if (StringUtil.isEmpty(newCommitId) || StringUtil.isEmpty(oldCommitId)) {
            logger.info("newCommitId or oldCommitId为空。无需下载额外exec文件。");
            return;
        }
        List<CoveragePublishVO> middlePublishes = coverageRepository.getMiddlePublishEntity(entity);

        if (CollectionUtil.isEmpty(middlePublishes)) {
            logger.info("获取中间部署记录为空。无需下载额外exec文件。");
            coverageMergeService.syncMergeSubVersionExecAndReport(entity, execPath, publicPath, logger);
            return;
        }
        Set<String> middleCommitIdList = new HashSet<>();
        for (CoveragePublishVO publishEntity : middlePublishes) {
            middleCommitIdList.add(publishEntity.getCommitId());
        }
        entity.setMiddleCommitIdList(middleCommitIdList);

        List<CoverageExecVO> execEntityList = coverageRepository.getMiddleExecList(middleCommitIdList, entity.getAppId(), entity.getVersionCode(), entity.getBranchName(), entity.getFlowLaneType(),
                entity.getDiffType());
        if (CollectionUtil.isNotEmpty(middleCommitIdList)) {
            logger.info("开始下载中间版本exec文件");
            for (CoverageExecVO execEntity : execEntityList) {
                try {
                    ossService.downloadFile(execEntity.getBucketName(), execEntity.getExecPath() + execEntity.getExecName(), execPath);
                } catch (Exception e) {
                    log.error("开始下载中间版本exec文件异常。bucketName = " + execEntity.getBucketName() + ",execName = " + execEntity.getExecName() + "。errorMsg = " + e.getMessage());
                }
            }
        }
        try {
            List<CoverageRecordBasicEntityDO> recordEntityList = coverageRepository.getLastRecordEntity(entity);
            if (CollectionUtil.isEmpty(recordEntityList)) {
                logger.info("未找到上一个生成成功的记录,无需下载xml报告。");
                coverageMergeService.syncMergeSubVersionExecAndReport(entity, execPath, publicPath, logger);
                return;
            }
            logger.info("下载并解析上一个版本xml报告 开始");
            CoverageRecordBasicEntityDO lastRecordEntity = recordEntityList.get(0);
            entity.setLastCommitId(lastRecordEntity.getCommitId());
            String remoteXmlKey = publicPath + "/" + lastRecordEntity.getCommitId() + "/xmlreport/" + lastRecordEntity.getCommitId() + ".xml";
            String localXmlPath = LOCAL_FILE_PATH + publicPath + "/" + entity.getCommitId() + "/xmlreport/";
            try {
                ossService.downloadFile(BucketEnum.COVERAGE_XML_BUCKET.getValue(), remoteXmlKey, localXmlPath);
            } catch (ServiceException e) {
                logger.warning("下载上一个版本xml报告异常。remoteXmlKey = " + remoteXmlKey + "。" + e.getMsg());
                throw new ServiceException("下载上一个版本xml报告异常。请稍后再试。" + e.getMessage());
            } catch (Exception e) {
                logger.warning("下载上一个版本xml报告异常。remoteXmlKey = " + remoteXmlKey + "。" + e.getMessage());
                throw new ServiceException("下载上一个版本xml报告异常。请稍后再试。" + e.getMessage());
            }
            if (new File(localXmlPath + "/" + lastRecordEntity.getCommitId() + ".xml").exists()) {
                logger.warning("下载并解析上一个版本xml报告。有上一个版本xml报告的 ---");
                entity.setReportDto(XmlUtils.dom4jParseXml(localXmlPath + "/" + lastRecordEntity.getCommitId() + ".xml"));
            } else {
                logger.warning("本地xml报告解析异常。localXmlPath = " + localXmlPath);
                throw new ServiceException("本地xml报告解析异常。请稍后再试。");
            }
            logger.info("下载并解析上一个版本xml报告 结束。" + JSON.toJSON(entity));
            logger.info("下载并解析上一个版本xml报告 结束。");
        } catch (Exception e) {
            log.error("下载并解析xml报告异常", e);
            throw new ServiceException("下载并解析xml报告异常。请稍后再试。" + e.getMessage());
        }
    }

    /**
     * 合并exec文件
     *
     * @param entity
     * @param execPath
     */
    private void emerge(CoverageRecordGenerateVO entity, String execPath, Logger logger) {
        File destFile = new File(execPath);
        if (!destFile.exists()) {
            logger.warning("destFile is not exist. execPath : " + execPath);
            throw new ServiceException("EXEC文件不存在");
        }
        if (getTxtFilesCount(destFile) >= 1) {
            try {
                logger.info("开始合并exec文件。" + execPath);
                jacocoService.executeMerge(execPath);
            } catch (Exception e) {
                throw new ServiceException("合并exec文件异常。" + e.getMessage());
            }
        }
        entity.setExecName("jacoco.exec");
    }

    /**
     * 作用：统计文件个数
     */
    public static int getTxtFilesCount(File srcFile) {
        int count = 0;
        if (srcFile == null) {
            throw new NullPointerException();
        }
        File[] files = srcFile.listFiles();
        for (File f : files) {
            if (f.isDirectory()) {
                getTxtFilesCount(f);
            } else {
                if (f.getName().endsWith(".exec")) {
                    count++;
                }
            }
        }
        return count;
    }

    public void completableFuture(final CoverageRecordGenerateVO entity, String publicPath, Logger logger, String sourcePath) {
        logger.info("开始执行jacoco代码覆盖率。versionCode = " + entity.getVersionCode() + ", appId = " + entity.getAppId() + ", diffType = " + entity.getDiffType());
        final CoverageDomainService generator = new CoverageDomainService();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String date = sdf.format(new Date());
        String reportPath = "/" + BucketEnum.COVERAGE_REPORT_BUCKET.getValue() + "/" + publicPath + "/" + entity.getCommitId() + "/" + date;
        String localPath = LOCAL_FILE_PATH + publicPath + "/" + entity.getCommitId() + "/";
        String localReportPath = LOCAL_FILE_PATH + publicPath + "/" + entity.getCommitId() + "/" + date;
        entity.setHtmlPath(reportPath + REPORT_INDEX);
        entity.setBucketName(BucketEnum.COVERAGE_REPORT_BUCKET.getValue());
        entity.setFileName(publicPath + "/" + entity.getCommitId() + "/" + date + REPORT_INDEX);
        try {
            jacocoService.create(sourcePath, entity, localReportPath, localPath, logger, config.getGitLabConfig().getUname(), config.getGitLabConfig().getPass(), config.getCoverageConfig().getProducts());
        } catch (ServiceException e) {
            log.error("生成报告失败", e);
            throw new ServiceException("生成报告失败。" + e.getMsg());
        } catch (Exception e) {
            log.error("生成报告异常", e);
            throw new ServiceException("生成报告异常。" + e.getMessage());
        }
        try {
            if (entity.getDiffType().equals(DiffTypeEnum.INCREMENT)) {
                ossService.uploadObject(localReportPath + "/xmlreport", BucketEnum.COVERAGE_XML_BUCKET.getValue());
            }
        } catch (Exception e) {
            log.error("上传xml报告异常", e);
            throw new ServiceException("上传xml报告异常。请稍后重试" + e.getMessage());
        } finally {
            File xmlReportFile = new File(localReportPath + "/xmlreport");
            if (xmlReportFile.exists()) {
                FileUtils.deleteDir(xmlReportFile);
            }
        }
        try {
            ossService.uploadObject(localReportPath + "/coveragereport", BucketEnum.COVERAGE_REPORT_BUCKET.getValue());
        } catch (Exception e) {
            log.error("上传html报告异常", e);
            throw new ServiceException("上传html报告异常。请稍后重试" + e.getMessage());
        } finally {
            File localReportFile = new File(localReportPath);
            if (localReportFile.exists()) {
                FileUtils.deleteDir(localReportFile);
            }
        }
        try {
            if (entity.getDiffType().equals(DiffTypeEnum.INCREMENT)) {
                ossService.uploadObject(localPath + "/xmlreport", BucketEnum.COVERAGE_XML_BUCKET.getValue());
            }
        } catch (Exception e) {
            log.error("上传xml报告异常", e);
            throw new ServiceException("上传xml报告异常。请稍后重试" + e.getMessage());
        } finally {
            File xmlReportFile = new File(localPath + "/xmlreport");
            if (xmlReportFile.exists()) {
                FileUtils.deleteDir(xmlReportFile);
            }
        }
        logger.info("保存报告成功");
    }

    /**
     * 删除缓存、临时文件
     *
     * @param fh
     * @param entity
     * @param parameter
     */
    private void deleteCache(FileHandler fh, CoverageRecordBasicVO entity, CoverageRecordGenerateParameter parameter) {
        if (fh != null) {
            fh.close();
        }
        // 生成完成删除缓存
        redisTemplate.delete(entity.getVersionCode() + entity.getAppId() + entity.getRecordType());
        // 手动触发同步覆盖率状态-成功/失败
        if (entity.getGenerateType() == GenerateTypeEnum.MANUAL) {
            CoverageRecordPageQuery query = new CoverageRecordPageQuery();
            query.setVersionCode(parameter.getVersionCode());
            query.setAppId(entity.getAppId());
            CoverageRecordVO coverageRecordVO = coverageRepository.getCoverageRecordList(query).get(0);
            CoverageStatusVO coverageStatusVO = coverageConverter.converter(coverageRecordVO);
            queryVersionCoverageRate(coverageStatusVO);
            reactiveEmitter.emit(TestEventEnums.TEST_CODE_COVER_GENERATE.toReactiveId(parameter.getVersionCode()), coverageStatusVO);
        }
        deleteTempFile(entity);
    }

    /**
     * 删除临时文件
     *
     * @param entity
     */
    private void deleteTempFile(CoverageRecordBasicVO entity) {
        try {
            String rmPath = LOCAL_FILE_PATH + entity.getVersionCode() + "-" + entity.getBranchName() + "-" + entity.getAppId() +
                    "/" + entity.getBranchName() + "/" + entity.getCommitId();
            FileUtils.deleteDir(new File(rmPath + "/exec"));
        } catch (Exception e) {
            log.warn("删除临时文件异常", e);
        }
    }

    public void editCoverage(CoverageRecordEditParameter parameter) {
        coverageRepository.editCoverage(parameter);
    }

    public void editReason(CoverageNotStandardReasonEditCommand command) {
        log.info("修改不达标原因CoverageNotStandardReasonEditCommand:{}", JSON.toJSONString(command));
        // 查询当前版本泳道状态
        checkVersionStatus(command.getVersionCode());
        CoverageNotStandardReasonEditEvent event = coverageConverter.converter(command);
        event.setAggregateId(aggregateIdGenerateService.generateId(AggregateType.SNOWFLAKE));
        CoverageRecordBasicEntityDO entityDO = new CoverageRecordBasicEntityDO();
        entityDO.setRemark(getCoverageRemark(command.getReasonList(), command.getCustomReason()));
        entityDO.setModifier(command.getTransactor().getUserName());
        entityDO.setModifierId(command.getTransactor().getUserId());
        entityDO.setGmtModified(new Date());
        coverageRepository.updateCoverageRemark(command.getVersionCode(), command.getAppId(), entityDO);
        apply(event);
    }

    /**
     * 校验版本状态
     */
    private void checkVersionStatus(String versionCode) {
        if (StringUtils.isBlank(versionCode)) {
            return;
        }
        VersionInfoVO versionInfoVO = coverageQueryDomainService.queryVersionVOByCode(versionCode);
        List<VersionStatus> statusList = Arrays.asList(VersionStatus.REGRESSED, VersionStatus.WAIT_AUDIT,
                VersionStatus.WAIT_RELEASE, VersionStatus.RELEASING, VersionStatus.ACCEPTING, VersionStatus.ACCEPTED,
                VersionStatus.CLOSED, VersionStatus.RELEASE_FAILED, VersionStatus.UNKNOWN);
        if (null == versionInfoVO || StringUtils.isBlank(versionInfoVO.getStatus())) {
            throw new ServiceException("当前版本状态异常，不可编辑不达标原因！");
        }
        VersionStatus versionStatus = VersionStatus.getByName(versionInfoVO.getStatus());
        if (statusList.contains(versionStatus)) {
            throw new ServiceException("当前版本状态[" + versionStatus.getValue() + "]不可编辑不达标原因！");
        }
    }

    public VersionInfoVO checkVersionInfo(String versionCode) {
        if (StringUtil.isEmpty(versionCode)) {
            throw new ServiceException("校验版本状态，版本号不能为空");
        }
        VersionInfoVO versionInfoVO = projectRpcService.findVersionBaseInfoQuery(versionCode);
        if (versionInfoVO != null && (VersionStatus.RELEASING.name().equals(versionInfoVO.getStatus())
                || VersionStatus.ACCEPTING.name().equals(versionInfoVO.getStatus())
                || VersionStatus.ACCEPTED.name().equals(versionInfoVO.getStatus())
                || VersionStatus.CLOSED.name().equals(versionInfoVO.getStatus()))) {
            throw new ServiceException("当前版本状态为[发布中、验收中、已验收或已关闭]，不能生成覆盖率。");
        }
        return versionInfoVO;
    }

    /**
     * 拼装代码覆盖率remark
     */
    private String getCoverageRemark(List<String> reasonList, String customReason) {
        String remark = Strings.EMPTY;
        AtomicInteger index = new AtomicInteger(1);
        if (CollectionUtil.isNotEmpty(reasonList)) {
            for (String reason : reasonList) {
                if (StringUtil.isNotBlank(reason)) {
                    remark += index + ". " + reason + "$";
                    index.getAndIncrement();
                }
            }
        }
        if (StringUtil.isNotEmpty(customReason)) {
            remark += index.get() + ". 自定义：" + customReason + "$";
        }
        return remark;
    }

    private void queryVersionCoverageRate(CoverageStatusVO vo) {
        vo.setBranchVersionRate(0);
        vo.setMasterVersionRate(0);
        if (StringUtil.isNotBlank(vo.getVersionCode())) {
            CoverageVersionRateQuery rateQuery = new CoverageVersionRateQuery(vo.getVersionCode());
            CoverageVersionRateVO rateVO = coverageQueryDomainService.getCoverageVersionRate(rateQuery);
            vo.setBranchVersionRate(rateVO.getBranchVersionRate());
            vo.setMasterVersionRate(rateVO.getMasterVersionRate());
        }
    }

    public void generateCoverageExec(ReleasedQcEvent event) {
        CoveragePublishQuery query = coverageConverter.converter(event);
        this.generateCoverageExec(query, DiffTypeEnum.INCREMENT);
    }

    public List<JacocoApplicationVO> checkApps(String flowCode, String planCode, String eventType) {
        final List<JacocoApplicationVO> apps = pipelineRpcService.listDeployApplicationQuery(flowCode, planCode);
        log.info("listDeployApplicationQuery_apps->{}", JsonUtil.toJSON(apps));
        if (CollectionUtil.isEmpty(apps)) {
            log.error("未找到应用部署信息，无需保存。 planCode:{}, flowCode: {}", planCode, flowCode);
            throw new ServiceException("未找到应用部署信息");
        }
        Set<JacocoApplicationVO> setWithoutDuplicates = new HashSet<>();
        for (JacocoApplicationVO obj : apps) {
            if (!setWithoutDuplicates.contains(obj) && checkFatEnv(obj, eventType)) {
                setWithoutDuplicates.add(obj);
            }
        }
        List<JacocoApplicationVO> listWithoutDuplicates = new ArrayList<>(setWithoutDuplicates);
        log.info("去重后的apps : {}", listWithoutDuplicates);
        return listWithoutDuplicates;
    }

    /**
     * 校验是否测试环境
     *
     * @param vo {@link JacocoApplicationVO}
     * @return true:是；false:不是
     */
    private boolean checkFatEnv(JacocoApplicationVO vo, String eventType) {
        if ("PidChangedEvent".equals(eventType) && vo.getType().equals(NamespaceTypeEnum.BASE)) {
            return true;
        }
        if (Objects.isNull(vo) || Objects.isNull(vo.getFlowLaneType())) {
            return false;
        }
        //非测试环境
        if (EnvEnum.FAT != vo.getEnv()) {
            return false;
        }
        //非功能测试/回归发布泳道
        if (!Arrays.asList(FlowLaneTypeEnum.FLOW_TEST, FlowLaneTypeEnum.FLOW_PROD).contains(vo.getFlowLaneType())) {
            return false;
        }
        //可生成空间类型
        return Objects.isNull(vo.getType()) || config.getEnableCoverageNamespace().contains(vo.getType().name());
    }

    public JacocoVersionVO checkJacocoVersionVO(String code) {
        final ListFlowVersionJacocoQuery query = new ListFlowVersionJacocoQuery();
        query.setFlowCode(code);
        final JacocoVersionVO jacocoVersion = pipelineRpcService.getJacocoVersion(query);
        if (jacocoVersion == null || CollectionUtil.isEmpty(jacocoVersion.getVersions())) {
            throw new ServiceException("版本信息jacocoVersion不能为空");
        }
        return jacocoVersion;
    }

    private CoverageRecordGenerateParameter makeGenerateParameter(String productCode, String versionCode, String versionName, List<String> appIdList, RecordTypeEnum recordType, User user,
                                                                  GenerateTypeEnum generateType, String flowLaneType) {
        CoverageRecordGenerateParameter parameter = new CoverageRecordGenerateParameter();
        parameter.setProductCode(productCode);
        parameter.setVersionCode(versionCode);
        parameter.setVersionName(versionName);
        parameter.setAppIdList(appIdList);
        parameter.setRecordType(recordType);
        parameter.setGenerateType(generateType);
        parameter.setCreator(user.getUserName());
        parameter.setCreatorId(user.getUserId());
        parameter.setTaskId(versionCode + DateUtil.dateToString(new Date(), "yyyyMMddHHmmss"));
        parameter.setFlowLaneType(flowLaneType);
        return parameter;
    }

    public void coverageTimeoutAbort() {
        log.info("执行覆盖率超时终止任务");
        THREAD_POOL_COVERAGE_ABORT.execute(this::coverageRecordAbort);
    }

    public void coverageRecordAbort() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -10);
        String timeoutDate = format.format(calendar.getTime());
        List<CoverageRecordBasicVO> coverageRecordBasicEntityList = coverageRepository.getCoverageRunningList(timeoutDate);
        if (CollectionUtil.isEmpty(coverageRecordBasicEntityList)) {
            log.info("没有需要终止的覆盖率超时任务");
            return;
        }
        coverageRecordBasicEntityList.forEach(this::timeoutAbort);
    }

    private void timeoutAbort(CoverageRecordBasicVO basicVO) {
        THREAD_POOL_COVERAGE_ABORT.submit(() -> {
            basicVO.setStatus(RecordStatusEnum.FAIL);
            basicVO.setGmtModified(new Date());
            basicVO.setRecordErrorMsg("超时终止");
            basicVO.setGmtCreate(null);
            log.info("修改覆盖率生成记录：{}", JSON.toJSONString(basicVO));
            coverageRepository.updateByPrimaryKeySelective(basicVO);

            CoverageRecordPageQuery query = new CoverageRecordPageQuery();
            query.setVersionCode(basicVO.getVersionCode());
            query.setAppId(basicVO.getAppId());
            CoverageRecordVO recordVO = coverageRepository.getCoverageRecordList(query).get(0);
            CoverageStatusVO coverageStatusVO = coverageConverter.converter(recordVO);
            queryVersionCoverageRate(coverageStatusVO);
            emit(TestEventEnums.TEST_CODE_COVER_GENERATE.toReactiveId(basicVO.getVersionCode()), coverageStatusVO);
        });
    }

    @Async
    public void cleanCoverageFile() {
        List<CoveragePublishVO> list;
        try {
            // 找到前一天的部署记录
            Calendar currentTime = Calendar.getInstance();
            int day = currentTime.get(Calendar.DATE);
            currentTime.set(Calendar.DATE, day - 1);
            Date startTime = DateUtil.getStartTimeOfDay(currentTime.getTime());
            Date endTime = DateUtil.getEndTimeOfDay(currentTime.getTime());
            list = coverageRepository.getPublishRecordLastDay(startTime, endTime);
        } catch (Exception e) {
            log.error("覆盖率源码文件每日定时清理任务 -> 获取部署记录异常!", e);
            return;
        }
        // 根据部署记录的版本删除文件
        if (CollectionUtil.isEmpty(list)) {
            log.info("前一天的部署记录为空，无需删除。");
            return;
        }
        for (CoveragePublishVO publishVO : list) {
            try {
                if (StringUtils.isNotBlank(publishVO.getVersionCode())) {
                    String rmPath = LOCAL_FILE_PATH + publishVO.getVersionCode() + "-" + publishVO.getBranchName() + "-" + publishVO.getAppId();
                    File dir = new File(rmPath);
                    FileUtils.deleteDir(dir);
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("源码清理任务执行异常，versionCode:{}", publishVO.getVersionCode(), e);
            }
        }
        log.info("覆盖率源码文件每日定时清理任务 -> 执行完成!");

    }

    /**
     * 是否部署过最新的commitId
     *
     * @param generateEntity
     * @return
     */
    private boolean isDeployedLatestCommitId(CoverageRecordGenerateVO generateEntity) {
        String latestCommitId = gitlabService.getLatestCommitId(generateEntity.getGitProjectId().intValue(), generateEntity.getBranchName());
        CoveragePublishVO publishVO = new CoveragePublishVO();
        publishVO.setVersionCode(generateEntity.getVersionCode());
        publishVO.setAppId(generateEntity.getAppId());
        publishVO.setBranchName(generateEntity.getBranchName());
        publishVO.setCommitId(latestCommitId);
        List<CoveragePublishVO> publishVOList = coverageRepository.getPublishRecordByCommitId(publishVO);
        if (CollectionUtil.isNotEmpty(publishVOList)) {
            return true;
        }
        return false;
    }

    @Async
    public void generateCoverageReportByValidData(CoverageRecordGenerateParameter parameter, List<VerifyGenerateVO> dataList) {
        log.info("generateCoverageReportByValidData_datalist:{}", JSON.toJSON(dataList));
        dataList.forEach(data -> {
            if (data.getStatus().equals(RecordStatusEnum.FAIL) || data.getStatus().equals(RecordStatusEnum.NEEDLESS)) {
                saveCoverageErrorMsg(data, parameter);
            } else if (data.getStatus().equals(RecordStatusEnum.SUCCEED)) {
                generateCoverageReport(parameter);
            }
        });
    }

    @Transactional
    public void updateCoverageBasic(ApplicationEditedEvent event) {
        if (Objects.isNull(event)
                || Objects.isNull(event.getProduct())
                || StringUtil.isBlank(event.getProduct().getCode())
                || Objects.isNull(event.getGitProjectId())) {
            log.info("编辑应用刷新覆盖率基准信息，参数缺失:{}", JSON.toJSON(event));
            return;
        }
        //查当前产品未验收的版本code
        List<VersionVO> versionList = projectRpcService.findAllDoingVersionList(Collections.singletonList(event.getProduct().getCode()));
        if (CollectionUtils.isEmpty(versionList)) {
            log.info("编辑应用刷新覆盖率基准信息，找不到进行中版本:{}", event.getAggregateId());
            return;
        }
        //根据当前应用和版本code，查basic数据
        List<String> excludeStatus = VersionStatus.getNotSupportedCoverageStatus();
        Set<String> versionCodes = versionList.stream().filter(item -> (!excludeStatus.contains(item.getStatus()))).map(VersionVO::getCode).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(versionCodes)) {
            log.info("编辑应用刷新覆盖率基准信息，没有支持生成覆盖率版本:{}", event.getAggregateId());
            return;
        }
        List<CoverageBranchBasicVO> basicList = coverageRepository.getCoverageBasicByVersionCodesAndAppId(versionCodes, event.getAppId());
        if (CollectionUtil.isEmpty(basicList)) {
            log.info("编辑应用刷新覆盖率基准信息，查询基准信息为空:{}", event.getAggregateId());
            return;
        }
        List<CoverageBranchBasicVO> needUpdateList = basicList.stream().filter(item -> (!event.getGitProjectId().equals(item.getGitProjectId()))).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(needUpdateList)) {
            log.info("编辑应用刷新覆盖率基准信息，项目id未发生变化，无需更改:{}", event.getAggregateId());
            return;
        }
        //更新gitProjectId+commitId
        List<CoverageBranchBasicVO> toUpdateList = new ArrayList<>();
        basicList.forEach(source -> {
            String commitId = gitlabService.getBranchCommitId(event.getGitProjectId().intValue(), source.getBasicBranchName());
            CoverageBranchBasicVO target = new CoverageBranchBasicVO();
            target.setId(source.getId());
            target.setGitProjectId(event.getGitProjectId());
            target.setBasicCommitId(commitId);
            target.preUpdate(event.getTransactor());
            toUpdateList.add(target);
        });
        coverageRepository.batchUpdateBranchBasic(toUpdateList);
    }

    public void initCoverageRecordsByVersionCode(String versionCode,
                                                 List<BaseApplicationDO> relatedApps,
                                                 String branchName,
                                                 String releaseBranchName) {
        //已保存的覆盖率记录
        List<String> branchNames = Arrays.asList(branchName, releaseBranchName);
        List<CoverageRecordBasicVO> savedList = coverageRepository.getExistCoverageRecords(versionCode, branchNames, AppTypeEnum.JAVA.name());

        //已无应用关联，删除覆盖率记录
        if (CollectionUtil.isEmpty(relatedApps) && CollectionUtil.isNotEmpty(savedList)) {
            doDeleteCancelApps(savedList);
            return;
        }

        //实际关联的应用
        CoverageRecordBasicVO basicEntity = new CoverageRecordBasicVO(null, versionCode, "", branchName, null, null, Boolean.TRUE, AppTypeEnum.JAVA);
        List<CoverageRecordBasicVO> relateList = buildNewList(relatedApps, basicEntity, releaseBranchName);

        //无覆盖率记录，新增全部
        if (CollectionUtil.isNotEmpty(relateList) && CollectionUtil.isEmpty(savedList)) {
            coverageRepository.batchInsert(relateList);
            return;
        }

        //对比更新
        doInsertCoverageRecordBasic(diffList(relateList, savedList));
        doRemoveCoverageRecordBasic(diffList(savedList, relateList));
    }

    private void doInsertCoverageRecordBasic(List<CoverageRecordBasicVO> needInsertList) {
        if (CollectionUtil.isEmpty(needInsertList)) {
           return;
        }
        for (CoverageRecordBasicVO entity : needInsertList) {
            entity.setStatus(RecordStatusEnum.INITIAL);
            coverageRepository.insertCoverageRecordBasic(entity);
        }
    }

    private void doRemoveCoverageRecordBasic(List<CoverageRecordBasicVO> needRemoveList) {
        if (CollectionUtil.isEmpty(needRemoveList)) {
            return;
        }
        for (CoverageRecordBasicVO entity : needRemoveList) {
            entity.setStatus(RecordStatusEnum.NEEDLESS);
            entity.setRemark(RecordRemarkEnum.CANCEL_APPLICATION_RELATION.getValue());
            entity.setGmtModified(new Date());
            entity.setEnable(Boolean.FALSE);
            coverageRepository.updateByPrimaryKeySelective(entity);
        }
    }
}
